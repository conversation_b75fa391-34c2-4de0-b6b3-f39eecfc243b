#!/usr/bin/env python3
"""
Vérification finale du projet LOGITRAK
Contrôle que tous les composants sont présents et fonctionnels
"""

import os
import sys

def check_files():
    """Vérifie la présence de tous les fichiers nécessaires"""
    print("📁 Vérification des fichiers...")
    
    required_files = [
        # Scripts principaux
        'main.py',
        'run_logitrak.py', 
        'launch_simple.py',
        
        # Modules core
        'database.py',
        'auth.py',
        'config.py',
        
        # Interface graphique
        'gui/__init__.py',
        'gui/login_window.py',
        'gui/main_window.py',
        'gui/product_manager.py',
        'gui/entry_manager.py',
        'gui/exit_manager.py',
        'gui/reports.py',
        
        # Utilitaires
        'demo_data.py',
        'test_imports.py',
        'test_db.py',
        
        # Documentation
        'README.md',
        'GUIDE_INSTALLATION.md',
        'PROJET_COMPLET.md',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MANQUANT")
            missing_files.append(file)
    
    return len(missing_files) == 0, missing_files

def check_imports():
    """Vérifie que tous les modules s'importent correctement"""
    print("\n🔍 Vérification des imports...")
    
    modules_to_test = [
        ('tkinter', 'Interface graphique'),
        ('sqlite3', 'Base de données'),
        ('database', 'Module base de données'),
        ('auth', 'Module authentification'),
        ('config', 'Module configuration'),
        ('gui.login_window', 'Fenêtre de connexion'),
        ('gui.main_window', 'Fenêtre principale')
    ]
    
    failed_imports = []
    for module, description in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError as e:
            print(f"❌ {module} - ERREUR: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0, failed_imports

def check_database():
    """Vérifie la base de données"""
    print("\n🗃️ Vérification de la base de données...")
    
    try:
        from database import Database
        
        # Test de création
        db = Database()
        print("✅ Création de la base de données")
        
        # Test des tables
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        conn.close()
        
        required_tables = ['users', 'produits', 'entrees', 'sorties']
        for table in required_tables:
            if table in tables:
                print(f"✅ Table {table}")
            else:
                print(f"❌ Table {table} - MANQUANTE")
                return False
        
        # Test de l'utilisateur admin
        user = db.verify_user("admin", "admin123")
        if user:
            print("✅ Utilisateur admin par défaut")
        else:
            print("❌ Utilisateur admin - PROBLÈME")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def check_configuration():
    """Vérifie la configuration"""
    print("\n⚙️ Vérification de la configuration...")
    
    try:
        from config import validate_config, get_config
        
        # Validation de la configuration
        errors = validate_config()
        if errors:
            print("❌ Erreurs de configuration:")
            for error in errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ Configuration valide")
        
        # Test d'accès aux paramètres
        app_name = get_config('app', 'name')
        if app_name == 'LOGITRAK':
            print("✅ Paramètres accessibles")
        else:
            print("❌ Problème d'accès aux paramètres")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur configuration: {e}")
        return False

def generate_summary():
    """Génère un résumé du projet"""
    print("\n📊 Résumé du projet LOGITRAK:")
    print("=" * 50)
    
    # Compter les fichiers
    total_files = 0
    for root, dirs, files in os.walk('.'):
        if '__pycache__' not in root:
            total_files += len([f for f in files if f.endswith('.py')])
    
    print(f"📁 Fichiers Python: {total_files}")
    
    # Compter les lignes de code
    total_lines = 0
    for root, dirs, files in os.walk('.'):
        if '__pycache__' not in root:
            for file in files:
                if file.endswith('.py'):
                    try:
                        with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                            total_lines += len(f.readlines())
                    except:
                        pass
    
    print(f"📝 Lignes de code: ~{total_lines}")
    
    # Fonctionnalités
    features = [
        "🔐 Système d'authentification",
        "🗂️ Gestion des produits",
        "➕ Gestion des entrées",
        "➖ Gestion des sorties", 
        "📊 Rapports et statistiques",
        "🗃️ Base de données SQLite",
        "🖼️ Interface graphique Tkinter",
        "📖 Documentation complète"
    ]
    
    print(f"✨ Fonctionnalités: {len(features)}")
    for feature in features:
        print(f"   {feature}")
    
    print("\n🚀 Scripts de lancement:")
    print("   • python run_logitrak.py (recommandé)")
    print("   • python main.py (complet)")
    print("   • python launch_simple.py (simplifié)")
    
    print("\n🔐 Connexion par défaut:")
    print("   • Utilisateur: admin")
    print("   • Mot de passe: admin123")

def main():
    """Fonction principale de vérification"""
    print("🧪 LOGITRAK - Vérification Finale du Projet")
    print("=" * 60)
    
    all_checks_passed = True
    
    # Vérification des fichiers
    files_ok, missing_files = check_files()
    if not files_ok:
        all_checks_passed = False
        print(f"\n❌ {len(missing_files)} fichier(s) manquant(s)")
    
    # Vérification des imports
    imports_ok, failed_imports = check_imports()
    if not imports_ok:
        all_checks_passed = False
        print(f"\n❌ {len(failed_imports)} import(s) échoué(s)")
    
    # Vérification de la base de données
    db_ok = check_database()
    if not db_ok:
        all_checks_passed = False
    
    # Vérification de la configuration
    config_ok = check_configuration()
    if not config_ok:
        all_checks_passed = False
    
    # Résumé
    generate_summary()
    
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("🎉 TOUTES LES VÉRIFICATIONS SONT PASSÉES !")
        print("✅ Le projet LOGITRAK est complet et fonctionnel")
        print("\n🚀 Pour lancer l'application:")
        print("   python run_logitrak.py")
        print("\n📖 Consultez GUIDE_INSTALLATION.md pour plus d'infos")
    else:
        print("❌ CERTAINES VÉRIFICATIONS ONT ÉCHOUÉ")
        print("🔧 Veuillez corriger les problèmes identifiés")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
