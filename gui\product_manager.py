import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font

class ProductManager:
    def __init__(self, parent, database, auth_manager):
        self.parent = parent
        self.database = database
        self.auth_manager = auth_manager
        self.products_tree = None
        
    def show(self):
        """Affiche l'interface de gestion des produits"""
        # Titre
        title_label = tk.Label(
            self.parent,
            text="🗂️ Gestion des Produits",
            font=('Arial', 16, 'bold'),
            bg='white'
        )
        title_label.pack(pady=20)
        
        # Frame pour les boutons (seulement pour admin)
        if self.auth_manager.is_admin():
            buttons_frame = tk.Frame(self.parent, bg='white')
            buttons_frame.pack(pady=10)
            
            add_btn = tk.Button(
                buttons_frame,
                text="➕ Ajouter un produit",
                font=('Arial', 10),
                bg='#27ae60',
                fg='white',
                command=self.add_product_dialog,
                cursor='hand2'
            )
            add_btn.pack(side='left', padx=5)
            
            edit_btn = tk.Button(
                buttons_frame,
                text="✏️ Modifier",
                font=('Arial', 10),
                bg='#f39c12',
                fg='white',
                command=self.edit_product_dialog,
                cursor='hand2'
            )
            edit_btn.pack(side='left', padx=5)
            
            delete_btn = tk.Button(
                buttons_frame,
                text="🗑️ Supprimer",
                font=('Arial', 10),
                bg='#e74c3c',
                fg='white',
                command=self.delete_product,
                cursor='hand2'
            )
            delete_btn.pack(side='left', padx=5)
        
        # Frame pour la recherche
        search_frame = tk.Frame(self.parent, bg='white')
        search_frame.pack(pady=10)
        
        tk.Label(search_frame, text="🔍 Rechercher:", bg='white').pack(side='left', padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_products)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side='left', padx=5)
        
        # Tableau des produits
        self.create_products_table()
        self.load_products()
    
    def create_products_table(self):
        """Crée le tableau des produits"""
        # Frame pour le tableau
        table_frame = tk.Frame(self.parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Treeview
        columns = ('Code', 'Nom', 'Catégorie', 'Unité', 'Stock')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configuration des colonnes
        self.products_tree.heading('Code', text='Code')
        self.products_tree.heading('Nom', text='Nom du produit')
        self.products_tree.heading('Catégorie', text='Catégorie')
        self.products_tree.heading('Unité', text='Unité')
        self.products_tree.heading('Stock', text='Stock actuel')
        
        self.products_tree.column('Code', width=100)
        self.products_tree.column('Nom', width=250)
        self.products_tree.column('Catégorie', width=150)
        self.products_tree.column('Unité', width=100)
        self.products_tree.column('Stock', width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.products_tree.xview)
        
        self.products_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout pour le tableau et scrollbars
        self.products_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # Configuration du redimensionnement
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
    
    def load_products(self):
        """Charge les produits dans le tableau"""
        # Vider le tableau
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # Charger les produits
        products = self.database.get_products()
        for product in products:
            self.products_tree.insert('', 'end', values=product[1:])  # Exclure l'ID
    
    def filter_products(self, *args):
        """Filtre les produits selon la recherche"""
        search_term = self.search_var.get().lower()
        
        # Vider le tableau
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # Charger les produits filtrés
        products = self.database.get_products()
        for product in products:
            # Rechercher dans le code, nom et catégorie
            if (search_term in product[1].lower() or  # code
                search_term in product[2].lower() or  # nom
                search_term in (product[3] or '').lower()):  # catégorie
                self.products_tree.insert('', 'end', values=product[1:])
    
    def add_product_dialog(self):
        """Ouvre la boîte de dialogue pour ajouter un produit"""
        dialog = ProductDialog(self.parent, "Ajouter un produit")
        if dialog.result:
            code, nom, categorie, unite = dialog.result
            product_id = self.database.add_product(code, nom, categorie, unite)
            if product_id:
                messagebox.showinfo("Succès", "Produit ajouté avec succès")
                self.load_products()
            else:
                messagebox.showerror("Erreur", "Ce code produit existe déjà")
    
    def edit_product_dialog(self):
        """Ouvre la boîte de dialogue pour modifier un produit"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un produit à modifier")
            return
        
        # Récupérer les données du produit sélectionné
        item = self.products_tree.item(selected[0])
        values = item['values']
        
        dialog = ProductDialog(self.parent, "Modifier le produit", values)
        if dialog.result:
            # TODO: Implémenter la modification
            messagebox.showinfo("Info", "Modification non implémentée dans cette version")
    
    def delete_product(self):
        """Supprime un produit"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("Sélection", "Veuillez sélectionner un produit à supprimer")
            return
        
        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer ce produit ?"):
            # TODO: Implémenter la suppression
            messagebox.showinfo("Info", "Suppression non implémentée dans cette version")


class ProductDialog:
    def __init__(self, parent, title, initial_values=None):
        self.result = None
        
        # Créer la fenêtre
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Centrer la fenêtre
        self.center_window()
        
        # Créer l'interface
        self.create_widgets(initial_values)
        
        # Focus sur le premier champ
        self.code_entry.focus()
        
        self.dialog.wait_window()
    
    def center_window(self):
        """Centre la fenêtre"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")
    
    def create_widgets(self, initial_values):
        """Crée les widgets du dialogue"""
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Code
        tk.Label(main_frame, text="Code produit:").grid(row=0, column=0, sticky='w', pady=5)
        self.code_entry = tk.Entry(main_frame, width=30)
        self.code_entry.grid(row=0, column=1, pady=5, padx=(10, 0))
        
        # Nom
        tk.Label(main_frame, text="Nom:").grid(row=1, column=0, sticky='w', pady=5)
        self.nom_entry = tk.Entry(main_frame, width=30)
        self.nom_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # Catégorie
        tk.Label(main_frame, text="Catégorie:").grid(row=2, column=0, sticky='w', pady=5)
        self.categorie_entry = tk.Entry(main_frame, width=30)
        self.categorie_entry.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # Unité
        tk.Label(main_frame, text="Unité:").grid(row=3, column=0, sticky='w', pady=5)
        self.unite_entry = tk.Entry(main_frame, width=30)
        self.unite_entry.grid(row=3, column=1, pady=5, padx=(10, 0))
        
        # Remplir avec les valeurs initiales si fournies
        if initial_values:
            self.code_entry.insert(0, initial_values[0])
            self.nom_entry.insert(0, initial_values[1])
            self.categorie_entry.insert(0, initial_values[2] or '')
            self.unite_entry.insert(0, initial_values[3])
        else:
            self.unite_entry.insert(0, 'pièce')
        
        # Boutons
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ok_btn = tk.Button(buttons_frame, text="OK", command=self.ok_clicked, bg='#3498db', fg='white')
        ok_btn.pack(side='left', padx=5)
        
        cancel_btn = tk.Button(buttons_frame, text="Annuler", command=self.cancel_clicked)
        cancel_btn.pack(side='left', padx=5)
    
    def ok_clicked(self):
        """Valide et ferme le dialogue"""
        code = self.code_entry.get().strip()
        nom = self.nom_entry.get().strip()
        categorie = self.categorie_entry.get().strip()
        unite = self.unite_entry.get().strip()
        
        if not code or not nom:
            messagebox.showerror("Erreur", "Le code et le nom sont obligatoires")
            return
        
        if not unite:
            unite = 'pièce'
        
        self.result = (code, nom, categorie, unite)
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Annule et ferme le dialogue"""
        self.dialog.destroy()
