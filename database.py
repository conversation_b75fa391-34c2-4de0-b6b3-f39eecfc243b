import sqlite3
import hashlib
from datetime import datetime
import os

class Database:
    def __init__(self, db_path="logitrak.db"):
        self.db_path = db_path
        self.init_database()
        
    def get_connection(self):
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialise la base de données avec les tables nécessaires"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Table des utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des produits
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                nom TEXT NOT NULL,
                categorie TEXT,
                unite TEXT DEFAULT 'pièce',
                stock_actuel REAL DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des entrées
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entrees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                produit_id INTEGER NOT NULL,
                date_entree TEXT NOT NULL,
                quantite REAL NOT NULL,
                source TEXT,
                note TEXT,
                user_id INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (produit_id) REFERENCES produits(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # Table des sorties
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sorties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                produit_id INTEGER NOT NULL,
                date_sortie TEXT NOT NULL,
                quantite REAL NOT NULL,
                destination TEXT,
                motif TEXT,
                user_id INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (produit_id) REFERENCES produits(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        conn.commit()
        
        # Créer un utilisateur admin par défaut s'il n'existe pas
        self.create_default_admin()
        conn.close()
    
    def create_default_admin(self):
        """Crée un utilisateur administrateur par défaut"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Vérifier si l'admin existe déjà
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if cursor.fetchone() is None:
            # Créer l'admin avec mot de passe hashé
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                ("admin", password_hash, "admin")
            )
            conn.commit()
        
        conn.close()
    
    def hash_password(self, password):
        """Hash un mot de passe"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_user(self, username, password):
        """Vérifie les identifiants d'un utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute(
            "SELECT id, username, role FROM users WHERE username = ? AND password = ?",
            (username, password_hash)
        )
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'username': user[1],
                'role': user[2]
            }
        return None
    
    def add_user(self, username, password, role='user'):
        """Ajoute un nouvel utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            password_hash = self.hash_password(password)
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                (username, password_hash, role)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def add_product(self, code, nom, categorie, unite='pièce'):
        """Ajoute un nouveau produit"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "INSERT INTO produits (code, nom, categorie, unite) VALUES (?, ?, ?, ?)",
                (code, nom, categorie, unite)
            )
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()
    
    def get_products(self):
        """Récupère tous les produits"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, code, nom, categorie, unite, stock_actuel 
            FROM produits 
            ORDER BY nom
        """)
        
        products = cursor.fetchall()
        conn.close()
        return products
    
    def update_product_stock(self, product_id, quantity_change):
        """Met à jour le stock d'un produit"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            "UPDATE produits SET stock_actuel = stock_actuel + ? WHERE id = ?",
            (quantity_change, product_id)
        )
        
        conn.commit()
        conn.close()
    
    def add_entry(self, produit_id, date_entree, quantite, source, note, user_id):
        """Ajoute une entrée de stock"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO entrees (produit_id, date_entree, quantite, source, note, user_id)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (produit_id, date_entree, quantite, source, note, user_id))
        
        # Mettre à jour le stock
        self.update_product_stock(produit_id, quantite)
        
        conn.commit()
        conn.close()
        return cursor.lastrowid
    
    def add_exit(self, produit_id, date_sortie, quantite, destination, motif, user_id):
        """Ajoute une sortie de stock"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO sorties (produit_id, date_sortie, quantite, destination, motif, user_id)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (produit_id, date_sortie, quantite, destination, motif, user_id))
        
        # Mettre à jour le stock (quantité négative)
        self.update_product_stock(produit_id, -quantite)
        
        conn.commit()
        conn.close()
        return cursor.lastrowid

    def get_recent_entries(self, limit=10):
        """Récupère les entrées récentes"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT e.date_entree, p.nom, e.quantite, e.source, u.username
            FROM entrees e
            JOIN produits p ON e.produit_id = p.id
            JOIN users u ON e.user_id = u.id
            ORDER BY e.created_at DESC
            LIMIT ?
        """, (limit,))

        entries = cursor.fetchall()
        conn.close()
        return entries

    def get_recent_exits(self, limit=10):
        """Récupère les sorties récentes"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.date_sortie, p.nom, s.quantite, s.destination, u.username
            FROM sorties s
            JOIN produits p ON s.produit_id = p.id
            JOIN users u ON s.user_id = u.id
            ORDER BY s.created_at DESC
            LIMIT ?
        """, (limit,))

        exits = cursor.fetchall()
        conn.close()
        return exits
