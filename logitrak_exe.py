#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LOGITRAK - Version optimisée pour exécutable
Point d'entrée principal pour la version .exe
"""

import sys
import os
import sqlite3
import tkinter as tk
from tkinter import messagebox

# Configuration pour PyInstaller
def get_resource_path(relative_path):
    """Obtient le chemin absolu vers une ressource"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def setup_exe_environment():
    """Configure l'environnement pour l'exécutable"""
    if getattr(sys, 'frozen', False):
        # Exécutable PyInstaller
        application_path = os.path.dirname(sys.executable)
        os.chdir(application_path)
        
        # Ajouter le chemin des ressources
        resource_path = get_resource_path('')
        if resource_path not in sys.path:
            sys.path.insert(0, resource_path)

def check_and_create_database():
    """Vérifie et crée la base de données si nécessaire"""
    db_path = "logitrak.db"
    
    # Supprimer le fichier journal s'il existe
    journal_path = db_path + "-journal"
    if os.path.exists(journal_path):
        try:
            os.remove(journal_path)
        except:
            pass
    
    # Créer la base de données si elle n'existe pas
    if not os.path.exists(db_path):
        try:
            from database import Database
            db = Database()
            
            # Ajouter quelques données de base
            products = [
                ("PC001", "Ordinateur portable", "Informatique", "pièce"),
                ("SOU001", "Souris", "Informatique", "pièce"),
                ("PAP001", "Papier A4", "Bureautique", "ramette"),
            ]
            
            for code, nom, categorie, unite in products:
                product_id = db.add_product(code, nom, categorie, unite)
                if product_id:
                    db.add_entry(product_id, "2024-12-01", 10, "Stock initial", "Initialisation", 1)
            
            return True
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'initialisation de la base de données:\n{str(e)}")
            return False
    
    return True

def launch_application():
    """Lance l'application principale"""
    try:
        # Importer les modules nécessaires
        from database import Database
        from auth import AuthManager
        from gui.login_window import LoginWindow
        
        # Essayer d'importer la fenêtre principale complète
        try:
            from gui.main_window import MainWindow
            use_full_interface = True
        except ImportError:
            # Fallback vers interface simplifiée
            use_full_interface = False
        
        class LogitrakExeApp:
            def __init__(self):
                self.database = Database()
                self.auth_manager = AuthManager(self.database)
                self.login_window = None
                self.main_window = None
                
            def start(self):
                self.show_login()
                
            def show_login(self):
                self.login_window = LoginWindow(self.auth_manager, self.on_login_success)
                self.login_window.show()
                
            def on_login_success(self):
                if self.login_window:
                    self.login_window.close()
                self.show_main_window()
                
            def show_main_window(self):
                if use_full_interface:
                    self.main_window = MainWindow(self.database, self.auth_manager, self.on_logout)
                    self.main_window.show()
                else:
                    self.show_simple_window()
                    
            def show_simple_window(self):
                """Interface simplifiée si l'interface complète n'est pas disponible"""
                window = tk.Tk()
                window.title("LOGITRAK - Gestion de Stock")
                window.geometry("600x400")
                window.configure(bg='#f0f0f0')
                
                # Titre
                title_label = tk.Label(
                    window,
                    text=f"🗂️ LOGITRAK\nBienvenue {self.auth_manager.get_username()}",
                    font=('Arial', 16, 'bold'),
                    bg='#f0f0f0',
                    justify='center'
                )
                title_label.pack(pady=30)
                
                # Informations
                info_text = f"""
Utilisateur connecté: {self.auth_manager.get_username()}
Rôle: {'Administrateur' if self.auth_manager.is_admin() else 'Utilisateur'}

📊 Statistiques:
                """
                
                try:
                    products = self.database.get_products()
                    entries = self.database.get_recent_entries(1000)
                    exits = self.database.get_recent_exits(1000)
                    
                    info_text += f"""
📦 Produits: {len(products)}
➕ Entrées: {len(entries)}
➖ Sorties: {len(exits)}
📈 Stock total: {sum(p[5] for p in products)} articles
                    """
                except Exception as e:
                    info_text += f"\nErreur: {e}"
                
                info_label = tk.Label(window, text=info_text, justify='left', bg='#f0f0f0')
                info_label.pack(pady=20)
                
                # Bouton de déconnexion
                logout_btn = tk.Button(
                    window,
                    text="🔓 Se déconnecter",
                    command=lambda: self.logout(window),
                    bg='#e74c3c',
                    fg='white',
                    font=('Arial', 12),
                    width=20,
                    height=2
                )
                logout_btn.pack(pady=30)
                
                # Message
                msg_label = tk.Label(
                    window,
                    text="Version exécutable simplifiée de LOGITRAK",
                    fg='gray',
                    bg='#f0f0f0'
                )
                msg_label.pack(side='bottom', pady=10)
                
                window.mainloop()
                
            def logout(self, window):
                if messagebox.askyesno("Déconnexion", "Êtes-vous sûr de vouloir vous déconnecter ?"):
                    window.destroy()
                    self.auth_manager.logout()
                    self.show_login()
                    
            def on_logout(self):
                if self.main_window:
                    self.main_window.close()
                self.show_login()
        
        # Lancer l'application
        app = LogitrakExeApp()
        app.start()
        
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors du lancement de l'application:\n{str(e)}")
        sys.exit(1)

def show_welcome_message():
    """Affiche un message de bienvenue pour la première utilisation"""
    if not os.path.exists("logitrak.db"):
        root = tk.Tk()
        root.withdraw()  # Cacher la fenêtre principale
        
        welcome_msg = """🎉 Bienvenue dans LOGITRAK !

Ceci est votre première utilisation de l'application.

🔐 Informations de connexion par défaut:
   • Utilisateur: admin
   • Mot de passe: admin123

⚠️ Important: Changez ce mot de passe après votre première connexion !

✨ LOGITRAK vous permet de:
   • Gérer vos produits et stocks
   • Enregistrer les entrées et sorties
   • Consulter des rapports détaillés
   • Suivre l'évolution de vos stocks

Cliquez sur OK pour commencer !"""
        
        messagebox.showinfo("Bienvenue dans LOGITRAK", welcome_msg)
        root.destroy()

def main():
    """Fonction principale pour l'exécutable"""
    try:
        # Configuration de l'environnement
        setup_exe_environment()
        
        # Vérifier et créer la base de données
        if not check_and_create_database():
            sys.exit(1)
        
        # Message de bienvenue pour la première utilisation
        show_welcome_message()
        
        # Lancer l'application
        launch_application()
        
    except KeyboardInterrupt:
        sys.exit(0)
    except Exception as e:
        # Afficher l'erreur dans une boîte de dialogue
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Erreur Fatale", f"Une erreur inattendue s'est produite:\n\n{str(e)}\n\nL'application va se fermer.")
        root.destroy()
        sys.exit(1)

if __name__ == "__main__":
    main()
