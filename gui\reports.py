import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates

class ReportsManager:
    def __init__(self, parent, database):
        self.parent = parent
        self.database = database
        
    def show(self):
        """Affiche l'interface des rapports"""
        # Titre
        title_label = tk.Label(
            self.parent,
            text="📊 Rapports et Statistiques",
            font=('Arial', 16, 'bold'),
            bg='white'
        )
        title_label.pack(pady=20)
        
        # Frame principal
        main_frame = tk.Frame(self.parent, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Créer les onglets
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """Crée les onglets pour les différents rapports"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True)
        
        # Onglet Résumé
        summary_frame = tk.Frame(notebook, bg='white')
        notebook.add(summary_frame, text="📈 Résumé")
        self.create_summary_tab(summary_frame)
        
        # Onglet Rapports mensuels
        monthly_frame = tk.Frame(notebook, bg='white')
        notebook.add(monthly_frame, text="📅 Mensuel")
        self.create_monthly_tab(monthly_frame)
        
        # Onglet Rapports annuels
        yearly_frame = tk.Frame(notebook, bg='white')
        notebook.add(yearly_frame, text="📆 Annuel")
        self.create_yearly_tab(yearly_frame)
        
        # Onglet Stock actuel
        stock_frame = tk.Frame(notebook, bg='white')
        notebook.add(stock_frame, text="📦 Stock")
        self.create_stock_tab(stock_frame)
    
    def create_summary_tab(self, parent):
        """Crée l'onglet résumé"""
        # Frame pour les statistiques rapides
        stats_frame = tk.LabelFrame(parent, text="Statistiques rapides", font=('Arial', 12, 'bold'), bg='white')
        stats_frame.pack(fill='x', padx=20, pady=20)
        
        # Créer une grille de statistiques
        stats_grid = tk.Frame(stats_frame, bg='white')
        stats_grid.pack(fill='x', padx=20, pady=20)
        
        # Statistiques (à implémenter avec de vraies données)
        stats = [
            ("Produits totaux", "0", "#3498db"),
            ("Entrées aujourd'hui", "0", "#27ae60"),
            ("Sorties aujourd'hui", "0", "#e74c3c"),
            ("Valeur stock", "0 €", "#f39c12")
        ]
        
        for i, (label, value, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_grid, bg=color, relief='raised', bd=2)
            stat_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')
            stats_grid.grid_columnconfigure(i, weight=1)
            
            tk.Label(stat_frame, text=value, font=('Arial', 20, 'bold'), bg=color, fg='white').pack(pady=5)
            tk.Label(stat_frame, text=label, font=('Arial', 10), bg=color, fg='white').pack(pady=(0, 10))
        
        # Graphique des mouvements récents
        chart_frame = tk.LabelFrame(parent, text="Mouvements des 7 derniers jours", font=('Arial', 12, 'bold'), bg='white')
        chart_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        self.create_movement_chart(chart_frame)
    
    def create_monthly_tab(self, parent):
        """Crée l'onglet des rapports mensuels"""
        # Sélection du mois
        selection_frame = tk.Frame(parent, bg='white')
        selection_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(selection_frame, text="Mois:", bg='white').pack(side='left', padx=5)
        
        self.month_var = tk.StringVar(value=datetime.now().strftime("%m/%Y"))
        month_entry = tk.Entry(selection_frame, textvariable=self.month_var, width=10)
        month_entry.pack(side='left', padx=5)
        
        generate_btn = tk.Button(
            selection_frame,
            text="Générer le rapport",
            command=self.generate_monthly_report,
            bg='#3498db',
            fg='white'
        )
        generate_btn.pack(side='left', padx=10)
        
        export_btn = tk.Button(
            selection_frame,
            text="Exporter PDF",
            command=self.export_monthly_pdf,
            bg='#e74c3c',
            fg='white'
        )
        export_btn.pack(side='left', padx=5)
        
        # Zone d'affichage du rapport
        self.monthly_report_frame = tk.Frame(parent, bg='white')
        self.monthly_report_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    def create_yearly_tab(self, parent):
        """Crée l'onglet des rapports annuels"""
        # Sélection de l'année
        selection_frame = tk.Frame(parent, bg='white')
        selection_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(selection_frame, text="Année:", bg='white').pack(side='left', padx=5)
        
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        year_entry = tk.Entry(selection_frame, textvariable=self.year_var, width=10)
        year_entry.pack(side='left', padx=5)
        
        generate_btn = tk.Button(
            selection_frame,
            text="Générer le rapport",
            command=self.generate_yearly_report,
            bg='#3498db',
            fg='white'
        )
        generate_btn.pack(side='left', padx=10)
        
        export_btn = tk.Button(
            selection_frame,
            text="Exporter PDF",
            command=self.export_yearly_pdf,
            bg='#e74c3c',
            fg='white'
        )
        export_btn.pack(side='left', padx=5)
        
        # Zone d'affichage du rapport
        self.yearly_report_frame = tk.Frame(parent, bg='white')
        self.yearly_report_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    def create_stock_tab(self, parent):
        """Crée l'onglet du stock actuel"""
        # Boutons d'action
        buttons_frame = tk.Frame(parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 Actualiser",
            command=self.refresh_stock,
            bg='#3498db',
            fg='white'
        )
        refresh_btn.pack(side='left', padx=5)
        
        export_btn = tk.Button(
            buttons_frame,
            text="📊 Exporter Excel",
            command=self.export_stock_excel,
            bg='#27ae60',
            fg='white'
        )
        export_btn.pack(side='left', padx=5)
        
        # Tableau du stock
        self.create_stock_table(parent)
    
    def create_stock_table(self, parent):
        """Crée le tableau du stock actuel"""
        table_frame = tk.Frame(parent, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Treeview
        columns = ('Code', 'Nom', 'Catégorie', 'Unité', 'Stock', 'Statut')
        self.stock_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configuration des colonnes
        for col in columns:
            self.stock_tree.heading(col, text=col)
            self.stock_tree.column(col, width=120)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.stock_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.stock_tree.xview)
        
        self.stock_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid
        self.stock_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Charger les données
        self.refresh_stock()
    
    def create_movement_chart(self, parent):
        """Crée un graphique des mouvements"""
        # Créer une figure matplotlib
        fig, ax = plt.subplots(figsize=(10, 4))
        
        # Données d'exemple (à remplacer par de vraies données)
        dates = [datetime.now() - timedelta(days=i) for i in range(7, 0, -1)]
        entries = [5, 8, 3, 12, 7, 9, 6]
        exits = [3, 6, 8, 5, 9, 4, 7]
        
        ax.plot(dates, entries, marker='o', label='Entrées', color='#27ae60', linewidth=2)
        ax.plot(dates, exits, marker='s', label='Sorties', color='#e74c3c', linewidth=2)
        
        ax.set_xlabel('Date')
        ax.set_ylabel('Quantité')
        ax.set_title('Mouvements de stock des 7 derniers jours')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Format des dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # Intégrer dans tkinter
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
    
    def refresh_stock(self):
        """Actualise le tableau du stock"""
        # Vider le tableau
        for item in self.stock_tree.get_children():
            self.stock_tree.delete(item)
        
        # Charger les produits
        products = self.database.get_products()
        for product in products:
            code, nom, categorie, unite, stock = product[1:6]
            
            # Déterminer le statut du stock
            if stock <= 0:
                statut = "Rupture"
                tags = ('rupture',)
            elif stock <= 5:  # Seuil d'alerte
                statut = "Faible"
                tags = ('faible',)
            else:
                statut = "Normal"
                tags = ('normal',)
            
            self.stock_tree.insert('', 'end', values=(code, nom, categorie or '', unite, stock, statut), tags=tags)
        
        # Configuration des couleurs
        self.stock_tree.tag_configure('rupture', background='#ffebee')
        self.stock_tree.tag_configure('faible', background='#fff3e0')
        self.stock_tree.tag_configure('normal', background='#e8f5e8')
    
    def generate_monthly_report(self):
        """Génère le rapport mensuel"""
        messagebox.showinfo("Info", "Génération du rapport mensuel en cours...")
    
    def generate_yearly_report(self):
        """Génère le rapport annuel"""
        messagebox.showinfo("Info", "Génération du rapport annuel en cours...")
    
    def export_monthly_pdf(self):
        """Exporte le rapport mensuel en PDF"""
        messagebox.showinfo("Info", "Export PDF non implémenté dans cette version")
    
    def export_yearly_pdf(self):
        """Exporte le rapport annuel en PDF"""
        messagebox.showinfo("Info", "Export PDF non implémenté dans cette version")
    
    def export_stock_excel(self):
        """Exporte le stock en Excel"""
        messagebox.showinfo("Info", "Export Excel non implémenté dans cette version")
