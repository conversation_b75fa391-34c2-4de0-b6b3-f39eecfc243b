@echo off
chcp 65001 >nul
echo ================================================
echo    🏗️ LOGITRAK - Construction de l'Exécutable
echo ================================================
echo.

echo 📋 Vérification de l'environnement...

REM Vérifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou pas dans le PATH
    echo 📥 Téléchargez Python depuis: https://python.org
    pause
    exit /b 1
)
echo ✅ Python détecté

REM Vérifier pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip n'est pas disponible
    pause
    exit /b 1
)
echo ✅ pip disponible

echo.
echo 📦 Installation des dépendances...

REM Installer PyInstaller
echo Installation de PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ Erreur lors de l'installation de PyInstaller
    pause
    exit /b 1
)

REM Installer Pillow pour l'icône
echo Installation de Pillow...
pip install Pillow
if errorlevel 1 (
    echo ⚠️ Pillow non installé, pas d'icône personnalisée
)

echo.
echo 🔨 Construction de l'exécutable...

REM Lancer le script de construction
python build_exe.py
if errorlevel 1 (
    echo ❌ Erreur lors de la construction
    pause
    exit /b 1
)

echo.
echo 📁 Vérification des fichiers créés...

if exist "dist\LOGITRAK.exe" (
    echo ✅ LOGITRAK.exe créé
    for %%A in ("dist\LOGITRAK.exe") do echo 📊 Taille: %%~zA octets
) else (
    echo ❌ LOGITRAK.exe non trouvé
    pause
    exit /b 1
)

if exist "dist\installer.bat" (
    echo ✅ installer.bat créé
) else (
    echo ⚠️ installer.bat non trouvé
)

if exist "dist\README_EXE.txt" (
    echo ✅ README_EXE.txt créé
) else (
    echo ⚠️ README_EXE.txt non trouvé
)

echo.
echo 📦 Création d'un package de distribution...

REM Créer un dossier de distribution avec date
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%"

set "package_name=LOGITRAK_v1.0_%datestamp%"

if exist "%package_name%" rmdir /s /q "%package_name%"
mkdir "%package_name%"

REM Copier les fichiers
copy "dist\LOGITRAK.exe" "%package_name%\"
copy "dist\installer.bat" "%package_name%\"
copy "dist\README_EXE.txt" "%package_name%\"
copy "README.md" "%package_name%\README_COMPLET.md"
copy "GUIDE_INSTALLATION.md" "%package_name%\"

REM Créer un fichier de version
echo LOGITRAK - Système de Gestion de Stock > "%package_name%\VERSION.txt"
echo Version: 1.0.0 >> "%package_name%\VERSION.txt"
echo Date de compilation: %datestamp% >> "%package_name%\VERSION.txt"
echo. >> "%package_name%\VERSION.txt"
echo Contenu du package: >> "%package_name%\VERSION.txt"
echo - LOGITRAK.exe : Application principale >> "%package_name%\VERSION.txt"
echo - installer.bat : Script d'installation automatique >> "%package_name%\VERSION.txt"
echo - README_EXE.txt : Guide d'utilisation >> "%package_name%\VERSION.txt"
echo - README_COMPLET.md : Documentation complète >> "%package_name%\VERSION.txt"
echo - GUIDE_INSTALLATION.md : Guide d'installation >> "%package_name%\VERSION.txt"

echo ✅ Package créé: %package_name%

echo.
echo 🎯 Création d'un fichier ZIP...

REM Créer un ZIP si PowerShell est disponible
powershell -command "Compress-Archive -Path '%package_name%\*' -DestinationPath '%package_name%.zip' -Force" 2>nul
if exist "%package_name%.zip" (
    echo ✅ Archive ZIP créée: %package_name%.zip
) else (
    echo ⚠️ Impossible de créer le ZIP automatiquement
    echo 📝 Créez manuellement un ZIP du dossier %package_name%
)

echo.
echo ================================================
echo    🎉 CONSTRUCTION TERMINÉE AVEC SUCCÈS !
echo ================================================
echo.
echo 📁 Fichiers créés:
echo    • %package_name%\LOGITRAK.exe
echo    • %package_name%\installer.bat
echo    • %package_name%\README_EXE.txt
if exist "%package_name%.zip" echo    • %package_name%.zip
echo.
echo 🚀 Pour distribuer l'application:
echo    1. Partagez le dossier '%package_name%' ou le fichier ZIP
echo    2. L'utilisateur final lance 'installer.bat' ou directement 'LOGITRAK.exe'
echo.
echo 🔐 Connexion par défaut:
echo    • Utilisateur: admin
echo    • Mot de passe: admin123
echo.
echo ✅ L'application est prête pour la distribution !
echo.
pause
