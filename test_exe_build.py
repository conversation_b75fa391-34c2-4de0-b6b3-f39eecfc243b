#!/usr/bin/env python3
"""
Test rapide pour vérifier la configuration avant la création de l'exécutable
"""

import sys
import os

def test_pyinstaller():
    """Test PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller disponible")
        return True
    except ImportError:
        print("❌ PyInstaller non installé")
        print("💡 Installez avec: pip install pyinstaller")
        return False

def test_main_modules():
    """Test des modules principaux"""
    modules = [
        'tkinter',
        'sqlite3', 
        'database',
        'auth',
        'gui.login_window'
    ]
    
    all_ok = True
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            all_ok = False
    
    return all_ok

def test_entry_point():
    """Test du point d'entrée pour l'exécutable"""
    if not os.path.exists('logitrak_exe.py'):
        print("❌ logitrak_exe.py manquant")
        return False
    
    print("✅ Point d'entrée logitrak_exe.py présent")
    return True

def test_build_script():
    """Test du script de construction"""
    if not os.path.exists('build_exe.py'):
        print("❌ build_exe.py manquant")
        return False
    
    print("✅ Script de construction présent")
    return True

def estimate_exe_size():
    """Estime la taille de l'exécutable"""
    try:
        import tkinter
        import sqlite3
        
        # Estimation basée sur les modules importés
        base_size = 30  # PyInstaller base
        tkinter_size = 15  # Tkinter
        sqlite_size = 5   # SQLite
        app_size = 10     # Code application
        
        total_mb = base_size + tkinter_size + sqlite_size + app_size
        print(f"📊 Taille estimée de l'exécutable: ~{total_mb} MB")
        
    except:
        print("⚠️ Impossible d'estimer la taille")

def main():
    """Test principal"""
    print("🧪 Test de Configuration pour Exécutable LOGITRAK")
    print("=" * 55)
    
    all_tests_passed = True
    
    # Tests
    if not test_pyinstaller():
        all_tests_passed = False
    
    if not test_main_modules():
        all_tests_passed = False
    
    if not test_entry_point():
        all_tests_passed = False
    
    if not test_build_script():
        all_tests_passed = False
    
    estimate_exe_size()
    
    print("\n" + "=" * 55)
    if all_tests_passed:
        print("🎉 Tous les tests sont passés !")
        print("✅ Prêt pour la création de l'exécutable")
        print("\n🚀 Commandes pour créer l'exécutable:")
        print("   python build_exe.py")
        print("   ou")
        print("   build_logitrak.bat")
    else:
        print("❌ Certains tests ont échoué")
        print("🔧 Corrigez les problèmes avant de continuer")
    
    print("=" * 55)

if __name__ == "__main__":
    main()
