@echo off
echo ================================================
echo    LOGITRAK - Installation
echo ================================================
echo.

REM Créer le dossier d'installation
if not exist "C:\LOGITRAK" mkdir "C:\LOGITRAK"

REM Copier l'exécutable
copy "LOGITRAK.exe" "C:\LOGITRAK\LOGITRAK.exe"

REM Créer un raccourci sur le bureau
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\Desktop\LOGITRAK.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\LOGITRAK\LOGITRAK.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "C:\LOGITRAK" >> CreateShortcut.vbs
echo oLink.Description = "LOGITRAK - Système de Gestion de Stock" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo.
echo ✅ Installation terminée!
echo 📁 Programme installé dans: C:\LOGITRAK
echo 🖥️ Raccourci créé sur le bureau
echo.
pause
