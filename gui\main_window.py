import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
from datetime import datetime
from .product_manager import ProductManager
from .entry_manager import EntryManager
from .exit_manager import ExitManager
from .reports import ReportsManager

class MainWindow:
    def __init__(self, database, auth_manager, on_logout):
        self.database = database
        self.auth_manager = auth_manager
        self.on_logout = on_logout
        self.window = None
        self.current_frame = None
        
        # Managers pour les différentes sections
        self.product_manager = None
        self.entry_manager = None
        self.exit_manager = None
        self.reports_manager = None
        
    def show(self):
        """Affiche la fenêtre principale"""
        self.window = tk.Tk()
        self.window.title("LOGITRAK - Gestion de Stock")
        self.window.geometry("1200x700")
        self.window.state('zoomed')  # Maximiser sur Windows
        
        # Style
        self.setup_styles()
        
        # Interface
        self.create_widgets()
        
        # Afficher la section produits par défaut
        self.show_products()
        
        self.window.mainloop()
    
    def setup_styles(self):
        """Configure les styles"""
        self.window.configure(bg='#ecf0f1')
        
        # Polices
        self.title_font = font.Font(family="Arial", size=14, weight="bold")
        self.button_font = font.Font(family="Arial", size=10)
        self.header_font = font.Font(family="Arial", size=12, weight="bold")
        
    def create_widgets(self):
        """Crée les widgets de l'interface"""
        # Barre supérieure
        self.create_header()
        
        # Frame principal avec sidebar et contenu
        main_container = tk.Frame(self.window, bg='#ecf0f1')
        main_container.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        # Sidebar
        self.create_sidebar(main_container)
        
        # Zone de contenu
        self.content_frame = tk.Frame(main_container, bg='white', relief='raised', bd=1)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
    def create_header(self):
        """Crée la barre supérieure"""
        header_frame = tk.Frame(self.window, bg='#2c3e50', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Titre à gauche
        title_label = tk.Label(
            header_frame,
            text="🗂️ LOGITRAK - Système de Gestion de Stock",
            font=self.title_font,
            bg='#2c3e50',
            fg='white'
        )
        title_label.pack(side='left', padx=20, pady=15)
        
        # Informations utilisateur à droite
        user_frame = tk.Frame(header_frame, bg='#2c3e50')
        user_frame.pack(side='right', padx=20, pady=15)
        
        # Date actuelle
        date_label = tk.Label(
            user_frame,
            text=datetime.now().strftime("%d/%m/%Y"),
            font=self.button_font,
            bg='#2c3e50',
            fg='#bdc3c7'
        )
        date_label.pack(side='right', padx=(20, 0))
        
        # Nom d'utilisateur et rôle
        user_info = f"👤 {self.auth_manager.get_username()}"
        if self.auth_manager.is_admin():
            user_info += " (Admin)"
        
        user_label = tk.Label(
            user_frame,
            text=user_info,
            font=self.button_font,
            bg='#2c3e50',
            fg='white'
        )
        user_label.pack(side='right')
        
    def create_sidebar(self, parent):
        """Crée la barre latérale de navigation"""
        sidebar = tk.Frame(parent, bg='#34495e', width=200)
        sidebar.pack(side='left', fill='y')
        sidebar.pack_propagate(False)
        
        # Titre sidebar
        sidebar_title = tk.Label(
            sidebar,
            text="Navigation",
            font=self.header_font,
            bg='#34495e',
            fg='white'
        )
        sidebar_title.pack(pady=(20, 30))
        
        # Boutons de navigation
        nav_buttons = [
            ("🗂️ Produits", self.show_products),
            ("➕ Entrées", self.show_entries),
            ("➖ Sorties", self.show_exits),
            ("📊 Rapports", self.show_reports),
            ("🔓 Déconnexion", self.logout)
        ]
        
        for text, command in nav_buttons:
            # Désactiver les rapports pour les utilisateurs non-admin
            if text == "📊 Rapports" and not self.auth_manager.is_admin():
                continue
                
            btn = tk.Button(
                sidebar,
                text=text,
                font=self.button_font,
                bg='#3498db',
                fg='white',
                width=18,
                height=2,
                command=command,
                cursor='hand2',
                relief='flat',
                bd=0
            )
            btn.pack(pady=5, padx=10)
            
            # Effet hover
            btn.bind("<Enter>", lambda e, b=btn: b.configure(bg='#2980b9'))
            btn.bind("<Leave>", lambda e, b=btn: b.configure(bg='#3498db'))
    
    def clear_content(self):
        """Vide la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_products(self):
        """Affiche la gestion des produits"""
        self.clear_content()
        if not self.product_manager:
            self.product_manager = ProductManager(self.content_frame, self.database, self.auth_manager)
        self.product_manager.show()
    
    def show_entries(self):
        """Affiche la gestion des entrées"""
        self.clear_content()
        if not self.entry_manager:
            self.entry_manager = EntryManager(self.content_frame, self.database, self.auth_manager)
        self.entry_manager.show()
    
    def show_exits(self):
        """Affiche la gestion des sorties"""
        self.clear_content()
        if not self.exit_manager:
            self.exit_manager = ExitManager(self.content_frame, self.database, self.auth_manager)
        self.exit_manager.show()
    
    def show_reports(self):
        """Affiche les rapports"""
        if not self.auth_manager.is_admin():
            messagebox.showerror("Accès refusé", "Seuls les administrateurs peuvent accéder aux rapports")
            return
            
        self.clear_content()
        if not self.reports_manager:
            self.reports_manager = ReportsManager(self.content_frame, self.database)
        self.reports_manager.show()
    
    def logout(self):
        """Déconnecte l'utilisateur"""
        if messagebox.askyesno("Déconnexion", "Êtes-vous sûr de vouloir vous déconnecter ?"):
            self.window.destroy()
            self.auth_manager.logout()
            self.on_logout()
    
    def close(self):
        """Ferme la fenêtre"""
        if self.window:
            self.window.destroy()
