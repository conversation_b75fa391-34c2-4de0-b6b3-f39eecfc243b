#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de création d'un exécutable (.exe) pour LOGITRAK
Utilise PyInstaller pour créer un fichier .exe autonome
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Vérifie si PyInstaller est installé"""
    try:
        import PyInstaller
        print("✅ PyInstaller est installé")
        return True
    except ImportError:
        print("❌ PyInstaller n'est pas installé")
        return False

def install_pyinstaller():
    """Installe PyInstaller"""
    print("📦 Installation de PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installé avec succès")
        return True
    except subprocess.CalledProcessError:
        print("❌ Erreur lors de l'installation de PyInstaller")
        return False

def create_spec_file():
    """Crée un fichier .spec personnalisé pour PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['logitrak_exe.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('gui', 'gui'),
        ('README.md', '.'),
        ('GUIDE_INSTALLATION.md', '.'),
        ('config.py', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'sqlite3',
        'matplotlib',
        'matplotlib.backends.backend_tkagg',
        'PIL',
        'reportlab',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='LOGITRAK',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('logitrak.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Fichier logitrak.spec créé")

def create_icon():
    """Crée une icône simple pour l'application"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Créer une image 256x256 avec fond bleu
        img = Image.new('RGBA', (256, 256), (52, 152, 219, 255))
        draw = ImageDraw.Draw(img)
        
        # Dessiner un carré blanc au centre
        draw.rectangle([64, 64, 192, 192], fill=(255, 255, 255, 255))
        
        # Essayer d'ajouter du texte
        try:
            font = ImageFont.truetype("arial.ttf", 40)
        except:
            font = ImageFont.load_default()
        
        # Ajouter le texte "LOG"
        text = "LOG"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (256 - text_width) // 2
        y = (256 - text_height) // 2 - 20
        draw.text((x, y), text, fill=(52, 152, 219, 255), font=font)
        
        # Ajouter le texte "TRAK"
        text2 = "TRAK"
        bbox2 = draw.textbbox((0, 0), text2, font=font)
        text_width2 = bbox2[2] - bbox2[0]
        x2 = (256 - text_width2) // 2
        y2 = y + text_height + 5
        draw.text((x2, y2), text2, fill=(52, 152, 219, 255), font=font)
        
        # Sauvegarder comme ICO
        img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ Icône icon.ico créée")
        return True
        
    except ImportError:
        print("⚠️ PIL/Pillow non disponible, pas d'icône créée")
        return False
    except Exception as e:
        print(f"⚠️ Erreur lors de la création de l'icône: {e}")
        return False

def build_executable():
    """Construit l'exécutable avec PyInstaller"""
    print("🔨 Construction de l'exécutable...")
    
    try:
        # Commande PyInstaller
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name=LOGITRAK",
            "--distpath=dist",
            "--workpath=build",
            "--specpath=.",
        ]
        
        # Ajouter l'icône si elle existe
        if os.path.exists('icon.ico'):
            cmd.extend(["--icon=icon.ico"])
        
        # Ajouter les données
        cmd.extend([
            "--add-data=gui;gui",
            "--add-data=README.md;.",
            "--add-data=GUIDE_INSTALLATION.md;.",
            "--add-data=config.py;.",
        ])
        
        # Ajouter les imports cachés
        hidden_imports = [
            "tkinter", "tkinter.ttk", "tkinter.messagebox", "tkinter.filedialog",
            "sqlite3", "matplotlib", "matplotlib.backends.backend_tkagg"
        ]
        
        for imp in hidden_imports:
            cmd.extend([f"--hidden-import={imp}"])
        
        # Fichier principal
        cmd.append("logitrak_exe.py")
        
        # Exécuter PyInstaller
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Exécutable créé avec succès!")
            return True
        else:
            print("❌ Erreur lors de la création de l'exécutable:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_installer_script():
    """Crée un script d'installation simple"""
    installer_content = '''@echo off
echo ================================================
echo    LOGITRAK - Installation
echo ================================================
echo.

REM Créer le dossier d'installation
if not exist "C:\\LOGITRAK" mkdir "C:\\LOGITRAK"

REM Copier l'exécutable
copy "LOGITRAK.exe" "C:\\LOGITRAK\\LOGITRAK.exe"

REM Créer un raccourci sur le bureau
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\LOGITRAK.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\LOGITRAK\\LOGITRAK.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "C:\\LOGITRAK" >> CreateShortcut.vbs
echo oLink.Description = "LOGITRAK - Système de Gestion de Stock" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo.
echo ✅ Installation terminée!
echo 📁 Programme installé dans: C:\\LOGITRAK
echo 🖥️ Raccourci créé sur le bureau
echo.
pause
'''
    
    with open('dist/installer.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ Script d'installation créé: dist/installer.bat")

def create_readme_exe():
    """Crée un README spécifique pour l'exécutable"""
    readme_content = '''# 🗂️ LOGITRAK - Version Exécutable

## 📦 Installation

### Méthode 1: Installation automatique
1. Double-cliquez sur `installer.bat`
2. Suivez les instructions
3. Un raccourci sera créé sur votre bureau

### Méthode 2: Installation manuelle
1. Copiez `LOGITRAK.exe` dans un dossier de votre choix
2. Double-cliquez sur `LOGITRAK.exe` pour lancer

## 🚀 Utilisation

1. **Lancement**: Double-cliquez sur l'icône LOGITRAK
2. **Connexion**: 
   - Utilisateur: `admin`
   - Mot de passe: `admin123`
3. **Navigation**: Utilisez le menu latéral pour accéder aux fonctions

## ✨ Fonctionnalités

- 🔐 Système d'authentification sécurisé
- 🗂️ Gestion complète des produits
- ➕ Enregistrement des entrées de stock
- ➖ Enregistrement des sorties de stock
- 📊 Rapports et statistiques détaillés
- 🗃️ Base de données intégrée (SQLite)

## 🔧 Dépannage

### L'application ne se lance pas
- Vérifiez que vous avez les droits d'administrateur
- Essayez de lancer en tant qu'administrateur (clic droit > "Exécuter en tant qu'administrateur")

### Erreur "Base de données verrouillée"
- Fermez complètement l'application
- Relancez LOGITRAK.exe

### Interface qui ne s'affiche pas
- Vérifiez que votre système supporte les applications graphiques
- Redémarrez votre ordinateur et réessayez

## 📁 Fichiers créés

L'application crée automatiquement:
- `logitrak.db` - Base de données des stocks
- Fichiers de configuration temporaires

## 🔒 Sécurité

- Toutes les données sont stockées localement
- Aucune connexion Internet requise
- Mots de passe chiffrés

## 📞 Support

Pour toute question ou problème:
1. Consultez ce fichier README
2. Vérifiez les messages d'erreur affichés
3. Redémarrez l'application

---

**Version Exécutable - LOGITRAK v1.0.0**
'''
    
    with open('dist/README_EXE.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ README pour l'exécutable créé")

def cleanup_build_files():
    """Nettoie les fichiers de construction temporaires"""
    print("🧹 Nettoyage des fichiers temporaires...")
    
    # Supprimer les dossiers de build
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✅ Dossier 'build' supprimé")
    
    # Supprimer le fichier .spec
    if os.path.exists('logitrak.spec'):
        os.remove('logitrak.spec')
        print("✅ Fichier 'logitrak.spec' supprimé")

def main():
    """Fonction principale de création de l'exécutable"""
    print("🏗️ LOGITRAK - Création d'un Exécutable")
    print("=" * 50)
    
    # Vérifier PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ Impossible d'installer PyInstaller")
            return False
    
    # Créer l'icône
    create_icon()
    
    # Créer le fichier spec
    create_spec_file()
    
    # Construire l'exécutable
    if not build_executable():
        print("❌ Échec de la création de l'exécutable")
        return False
    
    # Créer les fichiers d'accompagnement
    create_installer_script()
    create_readme_exe()
    
    # Nettoyer
    cleanup_build_files()
    
    print("\n" + "=" * 50)
    print("🎉 EXÉCUTABLE CRÉÉ AVEC SUCCÈS!")
    print("\n📁 Fichiers créés dans le dossier 'dist':")
    print("   • LOGITRAK.exe - Application principale")
    print("   • installer.bat - Script d'installation")
    print("   • README_EXE.txt - Guide d'utilisation")
    
    if os.path.exists('dist/LOGITRAK.exe'):
        size = os.path.getsize('dist/LOGITRAK.exe') / (1024 * 1024)
        print(f"\n📊 Taille de l'exécutable: {size:.1f} MB")
    
    print("\n🚀 Pour distribuer l'application:")
    print("   1. Copiez le dossier 'dist' complet")
    print("   2. Ou créez un ZIP avec le contenu de 'dist'")
    print("   3. L'utilisateur final lance 'installer.bat' ou 'LOGITRAK.exe'")
    
    print("\n✅ L'application est maintenant prête pour la distribution!")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    main()
