#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LOGITRAK - Script de lancement principal
Corrige les problèmes potentiels et lance l'application
"""

import sys
import os
import sqlite3

def check_and_fix_database():
    """Vérifie et corrige la base de données si nécessaire"""
    db_path = "logitrak.db"
    
    # Supprimer le fichier journal s'il existe
    journal_path = db_path + "-journal"
    if os.path.exists(journal_path):
        try:
            os.remove(journal_path)
            print("🔧 Fichier journal supprimé")
        except:
            pass
    
    # Tester la connexion à la base de données
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        print(f"✅ Base de données accessible avec {len(tables)} tables")
        return True
    except Exception as e:
        print(f"❌ Problème avec la base de données: {e}")
        # Recréer la base de données
        if os.path.exists(db_path):
            os.remove(db_path)
            print("🔧 Base de données corrompue supprimée")
        return False

def test_imports():
    """Teste les imports nécessaires"""
    print("🔍 Vérification des imports...")
    
    try:
        import tkinter as tk
        print("✅ tkinter disponible")
    except ImportError:
        print("❌ tkinter non disponible")
        return False
    
    try:
        from database import Database
        from auth import AuthManager
        from gui.login_window import LoginWindow
        print("✅ Modules LOGITRAK disponibles")
        return True
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def create_minimal_data():
    """Crée des données minimales pour tester"""
    try:
        from database import Database
        
        db = Database()
        
        # Ajouter quelques produits de base
        products = [
            ("PC001", "Ordinateur portable", "Informatique", "pièce"),
            ("SOU001", "Souris", "Informatique", "pièce"),
            ("PAP001", "Papier A4", "Bureautique", "ramette"),
        ]
        
        for code, nom, categorie, unite in products:
            product_id = db.add_product(code, nom, categorie, unite)
            if product_id:
                # Ajouter quelques entrées
                db.add_entry(product_id, "2024-12-01", 10, "Stock initial", "Initialisation", 1)
        
        print("✅ Données de test créées")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données: {e}")
        return False

def launch_application():
    """Lance l'application principale"""
    try:
        print("🚀 Lancement de LOGITRAK...")
        
        # Importer et lancer l'application
        from database import Database
        from auth import AuthManager
        from gui.login_window import LoginWindow
        from gui.main_window import MainWindow
        
        class LogitrakApp:
            def __init__(self):
                self.database = Database()
                self.auth_manager = AuthManager(self.database)
                
            def start(self):
                self.show_login()
                
            def show_login(self):
                login_window = LoginWindow(self.auth_manager, self.on_login_success)
                login_window.show()
                
            def on_login_success(self):
                main_window = MainWindow(self.database, self.auth_manager, self.on_logout)
                main_window.show()
                
            def on_logout(self):
                self.show_login()
        
        app = LogitrakApp()
        app.start()
        
    except ImportError:
        # Si matplotlib n'est pas disponible, utiliser la version simple
        print("⚠️ Matplotlib non disponible, lancement de la version simplifiée...")
        launch_simple_application()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        return False

def launch_simple_application():
    """Lance la version simplifiée sans matplotlib"""
    try:
        import tkinter as tk
        from tkinter import messagebox
        from database import Database
        from auth import AuthManager
        from gui.login_window import LoginWindow
        
        class SimpleMainWindow:
            def __init__(self, database, auth_manager, on_logout):
                self.database = database
                self.auth_manager = auth_manager
                self.on_logout = on_logout
                
            def show(self):
                window = tk.Tk()
                window.title("LOGITRAK - Version Simplifiée")
                window.geometry("600x400")
                
                # Interface simple
                tk.Label(
                    window, 
                    text=f"🗂️ LOGITRAK\nBienvenue {self.auth_manager.get_username()}",
                    font=('Arial', 16, 'bold'),
                    justify='center'
                ).pack(pady=50)
                
                # Statistiques
                try:
                    products = self.database.get_products()
                    entries = self.database.get_recent_entries(1000)
                    exits = self.database.get_recent_exits(1000)
                    
                    stats_text = f"""
📊 Statistiques actuelles:

📦 Produits: {len(products)}
➕ Entrées: {len(entries)}
➖ Sorties: {len(exits)}
📈 Stock total: {sum(p[5] for p in products)} articles
                    """
                    
                    tk.Label(window, text=stats_text, justify='left').pack(pady=20)
                    
                except Exception as e:
                    tk.Label(window, text=f"Erreur: {e}").pack(pady=20)
                
                # Boutons
                button_frame = tk.Frame(window)
                button_frame.pack(pady=30)
                
                tk.Button(
                    button_frame,
                    text="🔓 Se déconnecter",
                    command=lambda: self.logout(window),
                    bg='#e74c3c',
                    fg='white',
                    width=20,
                    height=2
                ).pack()
                
                # Message
                tk.Label(
                    window,
                    text="Pour l'interface complète, installez: pip install matplotlib reportlab",
                    fg='gray'
                ).pack(side='bottom', pady=10)
                
                window.mainloop()
            
            def logout(self, window):
                if messagebox.askyesno("Déconnexion", "Se déconnecter ?"):
                    window.destroy()
                    self.auth_manager.logout()
                    self.on_logout()
        
        class SimpleApp:
            def __init__(self):
                self.database = Database()
                self.auth_manager = AuthManager(self.database)
                
            def start(self):
                self.show_login()
                
            def show_login(self):
                login_window = LoginWindow(self.auth_manager, self.on_login_success)
                login_window.show()
                
            def on_login_success(self):
                main_window = SimpleMainWindow(self.database, self.auth_manager, self.on_logout)
                main_window.show()
                
            def on_logout(self):
                self.show_login()
        
        app = SimpleApp()
        app.start()
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement de la version simple: {e}")
        return False

def main():
    """Fonction principale"""
    print("🗂️ LOGITRAK - Système de Gestion de Stock")
    print("=" * 50)
    
    # Vérifications préliminaires
    if not test_imports():
        print("❌ Échec des vérifications d'imports")
        return
    
    if not check_and_fix_database():
        print("🔧 Recréation de la base de données...")
    
    # Créer des données de test si la base est vide
    create_minimal_data()
    
    print("\n🎯 Informations de connexion:")
    print("   Utilisateur: admin")
    print("   Mot de passe: admin123")
    print("\n" + "=" * 50)
    
    # Lancer l'application
    launch_application()

if __name__ == "__main__":
    main()
