#!/usr/bin/env python3
"""
Test simple de la base de données
"""

from database import Database

def test_basic_operations():
    """Test des opérations de base"""
    print("🧪 Test de la base de données...")
    
    try:
        # Créer la base de données
        db = Database()
        print("✅ Base de données créée")
        
        # Tester la connexion admin
        user = db.verify_user("admin", "admin123")
        if user:
            print(f"✅ Connexion admin réussie: {user}")
        else:
            print("❌ Échec de la connexion admin")
            return False
        
        # Ajouter un produit de test
        product_id = db.add_product("TEST001", "Produit de test", "Test", "pièce")
        if product_id:
            print(f"✅ Produit ajouté avec ID: {product_id}")
        else:
            print("❌ Échec de l'ajout du produit")
            return False
        
        # Récupérer les produits
        products = db.get_products()
        print(f"✅ {len(products)} produit(s) trouvé(s)")
        
        # Ajouter une entrée
        entry_id = db.add_entry(product_id, "2024-12-01", 10, "Test", "Entrée de test", 1)
        if entry_id:
            print(f"✅ Entrée ajoutée avec ID: {entry_id}")
        else:
            print("❌ Échec de l'ajout de l'entrée")
            return False
        
        # Ajouter une sortie
        exit_id = db.add_exit(product_id, "2024-12-01", 5, "Test", "Sortie de test", 1)
        if exit_id:
            print(f"✅ Sortie ajoutée avec ID: {exit_id}")
        else:
            print("❌ Échec de l'ajout de la sortie")
            return False
        
        # Vérifier le stock
        products = db.get_products()
        for product in products:
            if product[0] == product_id:
                stock = product[5]
                print(f"✅ Stock du produit: {stock} (attendu: 5)")
                break
        
        print("🎉 Tous les tests sont passés !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    test_basic_operations()
