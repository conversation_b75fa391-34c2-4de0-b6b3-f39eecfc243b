# 🗂️ LOGITRAK - Système de Gestion de Stock

LOGITRAK est une application de gestion de stock développée en Python avec Tkinter. Elle permet de gérer facilement les entrées et sorties d'articles, de suivre les stocks et de générer des rapports.

## ✨ Fonctionnalités

### 🔐 Système d'authentification
- Connexion avec nom d'utilisateur et mot de passe
- Deux niveaux d'accès : **Administrateur** et **Utilisateur**
- Gestion sécurisée des sessions

### 🗂️ Gestion des produits
- Ajout, modification et suppression de produits (admin uniquement)
- Recherche rapide dans la liste des produits
- Catégorisation et unités personnalisables
- Suivi automatique des stocks

### ➕ Enregistrement des entrées
- Saisie facile des entrées de stock
- Historique des entrées récentes
- Traçabilité avec source et notes
- Mise à jour automatique des stocks

### ➖ Enregistrement des sorties
- Gestion des sorties avec vérification de stock
- Alerte en cas de stock insuffisant
- Historique des sorties récentes
- Traçabilité avec destination et motif

### 📊 Rapports et statistiques
- Tableau de bord avec statistiques rapides
- Rapports mensuels et annuels
- Graphiques des mouvements de stock
- État du stock avec alertes (rupture, stock faible)
- Export PDF et Excel (à venir)

## 🚀 Installation et lancement

### Prérequis
- Python 3.7 ou supérieur
- Tkinter (généralement inclus avec Python)

### Installation des dépendances
```bash
pip install -r requirements.txt
```

### Lancement de l'application
```bash
python main.py
```

## 👤 Première connexion

### Compte administrateur par défaut
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`

⚠️ **Important :** Changez le mot de passe par défaut après la première connexion !

## 📖 Guide d'utilisation

### 1. Connexion
1. Lancez l'application avec `python main.py`
2. Saisissez vos identifiants
3. Cliquez sur "Se connecter"

### 2. Gestion des produits (Admin uniquement)
1. Cliquez sur "🗂️ Produits" dans le menu latéral
2. Utilisez "➕ Ajouter un produit" pour créer un nouveau produit
3. Remplissez les informations : code, nom, catégorie, unité
4. Utilisez la barre de recherche pour trouver rapidement un produit

### 3. Enregistrement des entrées
1. Cliquez sur "➕ Entrées" dans le menu latéral
2. Sélectionnez le produit dans la liste déroulante
3. Saisissez la quantité et les informations complémentaires
4. Cliquez sur "💾 Enregistrer l'entrée"

### 4. Enregistrement des sorties
1. Cliquez sur "➖ Sorties" dans le menu latéral
2. Sélectionnez le produit (le stock actuel s'affiche)
3. Saisissez la quantité à sortir
4. Renseignez la destination et le motif
5. Cliquez sur "📤 Enregistrer la sortie"

### 5. Consultation des rapports (Admin uniquement)
1. Cliquez sur "📊 Rapports" dans le menu latéral
2. Naviguez entre les onglets :
   - **📈 Résumé :** Vue d'ensemble et graphiques
   - **📅 Mensuel :** Rapports par mois
   - **📆 Annuel :** Rapports par année
   - **📦 Stock :** État actuel du stock

## 🗃️ Structure de la base de données

L'application utilise SQLite avec les tables suivantes :

- **users :** Utilisateurs et leurs rôles
- **produits :** Catalogue des produits
- **entrees :** Historique des entrées de stock
- **sorties :** Historique des sorties de stock

## 🔧 Configuration

### Rôles utilisateur
- **admin :** Accès complet (gestion produits, rapports, entrées/sorties)
- **user :** Accès limité (entrées/sorties uniquement)

### Personnalisation
- Modifiez les seuils d'alerte de stock dans `gui/reports.py`
- Ajustez les couleurs et styles dans les fichiers GUI
- Configurez les exports dans les modules de rapports

## 📁 Structure du projet

```
LOGITRAK/
├── main.py                 # Point d'entrée principal
├── database.py             # Gestion de la base de données
├── auth.py                 # Système d'authentification
├── requirements.txt        # Dépendances Python
├── README.md              # Documentation
├── gui/                   # Interface graphique
│   ├── __init__.py
│   ├── login_window.py    # Fenêtre de connexion
│   ├── main_window.py     # Fenêtre principale
│   ├── product_manager.py # Gestion des produits
│   ├── entry_manager.py   # Gestion des entrées
│   ├── exit_manager.py    # Gestion des sorties
│   └── reports.py         # Rapports et statistiques
└── logitrak.db           # Base de données SQLite (créée automatiquement)
```

## 🛠️ Développement

### Ajout de nouvelles fonctionnalités
1. Modifiez `database.py` pour les nouvelles tables/requêtes
2. Créez ou modifiez les modules GUI correspondants
3. Mettez à jour les permissions dans `auth.py` si nécessaire

### Tests
- Testez avec différents rôles d'utilisateur
- Vérifiez les validations de données
- Testez les cas limites (stock négatif, etc.)

## 🐛 Dépannage

### Problèmes courants
- **Erreur de base de données :** Vérifiez les permissions du fichier `logitrak.db`
- **Interface qui ne s'affiche pas :** Vérifiez que Tkinter est installé
- **Erreur d'import :** Vérifiez que toutes les dépendances sont installées

### Support
Pour signaler un bug ou demander une fonctionnalité, créez un ticket dans le système de gestion de projet.

## 📄 Licence

Ce projet est développé pour un usage interne. Tous droits réservés.

---

**Version :** 1.0.0  
**Dernière mise à jour :** Décembre 2024
