#!/usr/bin/env python3
"""
Lanceur simplifié de LOGITRAK sans dépendances matplotlib
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database
from auth import AuthManager
from gui.login_window import LoginWindow

# Version simplifiée de MainWindow sans matplotlib
class SimpleMainWindow:
    def __init__(self, database, auth_manager, on_logout):
        self.database = database
        self.auth_manager = auth_manager
        self.on_logout = on_logout
        self.window = None
        
    def show(self):
        """Affiche la fenêtre principale simplifiée"""
        self.window = tk.Tk()
        self.window.title("LOGITRAK - Gestion de Stock (Version Simple)")
        self.window.geometry("800x600")
        
        # Interface simplifiée
        self.create_widgets()
        self.window.mainloop()
    
    def create_widgets(self):
        """Crée une interface simplifiée"""
        # Titre
        title_label = tk.Label(
            self.window,
            text=f"🗂️ LOGITRAK - Bienvenue {self.auth_manager.get_username()}",
            font=('Arial', 16, 'bold'),
            bg='#2c3e50',
            fg='white',
            height=2
        )
        title_label.pack(fill='x')
        
        # Frame principal
        main_frame = tk.Frame(self.window)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Informations utilisateur
        user_info = f"Utilisateur connecté: {self.auth_manager.get_username()}"
        if self.auth_manager.is_admin():
            user_info += " (Administrateur)"
        else:
            user_info += " (Utilisateur)"
        
        info_label = tk.Label(main_frame, text=user_info, font=('Arial', 12))
        info_label.pack(pady=10)
        
        # Statistiques rapides
        stats_frame = tk.LabelFrame(main_frame, text="Statistiques", font=('Arial', 12, 'bold'))
        stats_frame.pack(fill='x', pady=20)
        
        try:
            products = self.database.get_products()
            entries = self.database.get_recent_entries(1000)
            exits = self.database.get_recent_exits(1000)
            
            stats_text = f"""
📦 Produits enregistrés: {len(products)}
➕ Entrées totales: {len(entries)}
➖ Sorties totales: {len(exits)}
📊 Stock total: {sum(p[5] for p in products)} articles
            """
            
            tk.Label(stats_frame, text=stats_text, justify='left', font=('Arial', 10)).pack(pady=10)
            
        except Exception as e:
            tk.Label(stats_frame, text=f"Erreur lors du chargement des statistiques: {e}").pack(pady=10)
        
        # Boutons d'action
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(pady=20)
        
        if self.auth_manager.is_admin():
            tk.Button(
                buttons_frame,
                text="🗂️ Voir les produits",
                command=self.show_products,
                bg='#3498db',
                fg='white',
                width=20,
                height=2
            ).pack(side='left', padx=10)
        
        tk.Button(
            buttons_frame,
            text="➕ Nouvelle entrée",
            command=self.show_entry_form,
            bg='#27ae60',
            fg='white',
            width=20,
            height=2
        ).pack(side='left', padx=10)
        
        tk.Button(
            buttons_frame,
            text="➖ Nouvelle sortie",
            command=self.show_exit_form,
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2
        ).pack(side='left', padx=10)
        
        # Bouton de déconnexion
        tk.Button(
            main_frame,
            text="🔓 Se déconnecter",
            command=self.logout,
            bg='#95a5a6',
            fg='white',
            width=20
        ).pack(side='bottom', pady=20)
        
        # Message d'information
        info_text = """
🎉 LOGITRAK est opérationnel !

Cette version simplifiée vous permet de tester les fonctionnalités de base.
Pour accéder à l'interface complète avec graphiques et rapports,
installez les dépendances avec: pip install -r requirements.txt

Puis lancez: python main.py
        """
        
        tk.Label(main_frame, text=info_text, justify='center', fg='#7f8c8d').pack(pady=20)
    
    def show_products(self):
        """Affiche la liste des produits"""
        messagebox.showinfo("Produits", "Fonctionnalité disponible dans l'interface complète")
    
    def show_entry_form(self):
        """Affiche le formulaire d'entrée"""
        messagebox.showinfo("Entrées", "Fonctionnalité disponible dans l'interface complète")
    
    def show_exit_form(self):
        """Affiche le formulaire de sortie"""
        messagebox.showinfo("Sorties", "Fonctionnalité disponible dans l'interface complète")
    
    def logout(self):
        """Déconnecte l'utilisateur"""
        if messagebox.askyesno("Déconnexion", "Êtes-vous sûr de vouloir vous déconnecter ?"):
            self.window.destroy()
            self.auth_manager.logout()
            self.on_logout()

class SimpleLauncherApp:
    def __init__(self):
        self.database = None
        self.auth_manager = None
        self.login_window = None
        self.main_window = None
        
    def start(self):
        """Démarre l'application"""
        try:
            # Initialiser la base de données
            self.database = Database()
            
            # Initialiser le gestionnaire d'authentification
            self.auth_manager = AuthManager(self.database)
            
            # Afficher la fenêtre de connexion
            self.show_login()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du démarrage de l'application:\n{str(e)}")
            sys.exit(1)
    
    def show_login(self):
        """Affiche la fenêtre de connexion"""
        self.login_window = LoginWindow(self.auth_manager, self.on_login_success)
        self.login_window.show()
    
    def on_login_success(self):
        """Appelé après une connexion réussie"""
        if self.login_window:
            self.login_window.close()
        self.show_main_window()
    
    def show_main_window(self):
        """Affiche la fenêtre principale"""
        self.main_window = SimpleMainWindow(self.database, self.auth_manager, self.on_logout)
        self.main_window.show()
    
    def on_logout(self):
        """Appelé lors de la déconnexion"""
        if self.main_window:
            self.main_window.window.destroy()
        self.show_login()

def main():
    """Point d'entrée principal"""
    print("🚀 Lancement de LOGITRAK (Version Simple)")
    print("Compte par défaut: admin / admin123")
    
    try:
        app = SimpleLauncherApp()
        app.start()
    except KeyboardInterrupt:
        print("\nApplication interrompue par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"Erreur fatale: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
