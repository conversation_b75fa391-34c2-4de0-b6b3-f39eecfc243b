import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class EntryManager:
    def __init__(self, parent, database, auth_manager):
        self.parent = parent
        self.database = database
        self.auth_manager = auth_manager
        
    def show(self):
        """Affiche l'interface de gestion des entrées"""
        # Titre
        title_label = tk.Label(
            self.parent,
            text="➕ Enregistrement des Entrées",
            font=('Arial', 16, 'bold'),
            bg='white'
        )
        title_label.pack(pady=20)
        
        # Frame principal
        main_frame = tk.Frame(self.parent, bg='white')
        main_frame.pack(fill='both', expand=True, padx=50, pady=20)
        
        # Formulaire d'entrée
        self.create_entry_form(main_frame)
    
    def create_entry_form(self, parent):
        """Crée le formulaire d'entrée"""
        # Frame pour le formulaire
        form_frame = tk.LabelFrame(parent, text="Nouvelle entrée", font=('Arial', 12, 'bold'), bg='white')
        form_frame.pack(fill='x', pady=20)
        
        # Grid configuration
        form_frame.grid_columnconfigure(1, weight=1)
        
        # Sélection du produit
        tk.Label(form_frame, text="Produit:", bg='white').grid(row=0, column=0, sticky='w', padx=10, pady=10)
        
        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(form_frame, textvariable=self.product_var, state='readonly', width=40)
        self.product_combo.grid(row=0, column=1, sticky='ew', padx=10, pady=10)
        
        # Charger les produits
        self.load_products()
        
        # Date d'entrée
        tk.Label(form_frame, text="Date d'entrée:", bg='white').grid(row=1, column=0, sticky='w', padx=10, pady=10)
        
        self.date_var = tk.StringVar(value=datetime.now().strftime("%d/%m/%Y"))
        date_entry = tk.Entry(form_frame, textvariable=self.date_var, width=20)
        date_entry.grid(row=1, column=1, sticky='w', padx=10, pady=10)
        
        # Quantité
        tk.Label(form_frame, text="Quantité:", bg='white').grid(row=2, column=0, sticky='w', padx=10, pady=10)
        
        self.quantity_var = tk.StringVar()
        quantity_entry = tk.Entry(form_frame, textvariable=self.quantity_var, width=20)
        quantity_entry.grid(row=2, column=1, sticky='w', padx=10, pady=10)
        
        # Source
        tk.Label(form_frame, text="Source:", bg='white').grid(row=3, column=0, sticky='w', padx=10, pady=10)
        
        self.source_var = tk.StringVar()
        source_entry = tk.Entry(form_frame, textvariable=self.source_var, width=40)
        source_entry.grid(row=3, column=1, sticky='ew', padx=10, pady=10)
        
        # Note
        tk.Label(form_frame, text="Note:", bg='white').grid(row=4, column=0, sticky='nw', padx=10, pady=10)
        
        self.note_text = tk.Text(form_frame, height=3, width=40)
        self.note_text.grid(row=4, column=1, sticky='ew', padx=10, pady=10)
        
        # Boutons
        buttons_frame = tk.Frame(form_frame, bg='white')
        buttons_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        save_btn = tk.Button(
            buttons_frame,
            text="💾 Enregistrer l'entrée",
            font=('Arial', 10, 'bold'),
            bg='#27ae60',
            fg='white',
            command=self.save_entry,
            cursor='hand2',
            width=20
        )
        save_btn.pack(side='left', padx=10)
        
        clear_btn = tk.Button(
            buttons_frame,
            text="🗑️ Effacer",
            font=('Arial', 10),
            bg='#95a5a6',
            fg='white',
            command=self.clear_form,
            cursor='hand2',
            width=15
        )
        clear_btn.pack(side='left', padx=10)
        
        # Historique des entrées récentes
        self.create_recent_entries(parent)
    
    def load_products(self):
        """Charge la liste des produits dans le combobox"""
        products = self.database.get_products()
        product_list = [f"{product[1]} - {product[2]}" for product in products]  # code - nom
        self.product_combo['values'] = product_list
        
        # Stocker la correspondance code -> id
        self.product_map = {f"{product[1]} - {product[2]}": product[0] for product in products}
    
    def save_entry(self):
        """Enregistre une nouvelle entrée"""
        # Validation
        if not self.product_var.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un produit")
            return
        
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Erreur", "Veuillez saisir une quantité valide (nombre positif)")
            return
        
        # Validation de la date
        try:
            date_obj = datetime.strptime(self.date_var.get(), "%d/%m/%Y")
            date_str = date_obj.strftime("%Y-%m-%d")
        except ValueError:
            messagebox.showerror("Erreur", "Format de date invalide (JJ/MM/AAAA)")
            return
        
        # Récupérer les données
        product_id = self.product_map[self.product_var.get()]
        source = self.source_var.get().strip()
        note = self.note_text.get("1.0", tk.END).strip()
        user_id = self.auth_manager.get_user_id()
        
        # Enregistrer dans la base de données
        try:
            self.database.add_entry(product_id, date_str, quantity, source, note, user_id)
            messagebox.showinfo("Succès", f"Entrée enregistrée avec succès\nQuantité: {quantity}")
            self.clear_form()
            self.load_recent_entries()  # Rafraîchir l'historique
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
    
    def clear_form(self):
        """Efface le formulaire"""
        self.product_var.set("")
        self.date_var.set(datetime.now().strftime("%d/%m/%Y"))
        self.quantity_var.set("")
        self.source_var.set("")
        self.note_text.delete("1.0", tk.END)
    
    def create_recent_entries(self, parent):
        """Crée la section des entrées récentes"""
        # Frame pour l'historique
        history_frame = tk.LabelFrame(parent, text="Entrées récentes", font=('Arial', 12, 'bold'), bg='white')
        history_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # Tableau des entrées récentes
        columns = ('Date', 'Produit', 'Quantité', 'Source', 'Utilisateur')
        self.entries_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=8)
        
        # Configuration des colonnes
        self.entries_tree.heading('Date', text='Date')
        self.entries_tree.heading('Produit', text='Produit')
        self.entries_tree.heading('Quantité', text='Quantité')
        self.entries_tree.heading('Source', text='Source')
        self.entries_tree.heading('Utilisateur', text='Utilisateur')
        
        self.entries_tree.column('Date', width=100)
        self.entries_tree.column('Produit', width=200)
        self.entries_tree.column('Quantité', width=100)
        self.entries_tree.column('Source', width=150)
        self.entries_tree.column('Utilisateur', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(history_frame, orient='vertical', command=self.entries_tree.yview)
        self.entries_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.entries_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Charger les entrées récentes
        self.load_recent_entries()
    
    def load_recent_entries(self):
        """Charge les entrées récentes"""
        # Vider le tableau
        for item in self.entries_tree.get_children():
            self.entries_tree.delete(item)

        # Récupérer les entrées récentes
        try:
            entries = self.database.get_recent_entries(20)
            for entry in entries:
                date_str = entry[0]
                # Convertir la date au format français
                try:
                    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                    date_formatted = date_obj.strftime("%d/%m/%Y")
                except:
                    date_formatted = date_str

                self.entries_tree.insert('', 'end', values=(
                    date_formatted,
                    entry[1],  # nom produit
                    entry[2],  # quantité
                    entry[3] or '',  # source
                    entry[4]   # utilisateur
                ))
        except Exception as e:
            self.entries_tree.insert('', 'end', values=('--', f'Erreur: {str(e)}', '--', '--', '--'))
