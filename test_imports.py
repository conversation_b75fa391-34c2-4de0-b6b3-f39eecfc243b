#!/usr/bin/env python3
"""
Script de test pour vérifier les imports et la configuration
"""

def test_imports():
    """Teste tous les imports nécessaires"""
    print("🔍 Test des imports...")
    
    try:
        import tkinter as tk
        print("✅ tkinter - OK")
    except ImportError as e:
        print(f"❌ tkinter - ERREUR: {e}")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 - OK")
    except ImportError as e:
        print(f"❌ sqlite3 - ERREUR: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✅ matplotlib - OK")
    except ImportError as e:
        print(f"❌ matplotlib - ERREUR: {e}")
        print("   Installez avec: pip install matplotlib")
        return False
    
    try:
        from database import Database
        print("✅ database - OK")
    except ImportError as e:
        print(f"❌ database - ERREUR: {e}")
        return False
    
    try:
        from auth import AuthManager
        print("✅ auth - OK")
    except ImportError as e:
        print(f"❌ auth - ERREUR: {e}")
        return False
    
    try:
        from gui.login_window import LoginWindow
        print("✅ gui.login_window - OK")
    except ImportError as e:
        print(f"❌ gui.login_window - ERREUR: {e}")
        return False
    
    try:
        from gui.main_window import MainWindow
        print("✅ gui.main_window - OK")
    except ImportError as e:
        print(f"❌ gui.main_window - ERREUR: {e}")
        return False
    
    return True

def test_database():
    """Teste la création et l'initialisation de la base de données"""
    print("\n🗃️ Test de la base de données...")
    
    try:
        from database import Database
        db = Database("test_logitrak.db")
        print("✅ Création de la base de données - OK")
        
        # Test de connexion
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        
        expected_tables = ['users', 'produits', 'entrees', 'sorties']
        found_tables = [table[0] for table in tables]
        
        for table in expected_tables:
            if table in found_tables:
                print(f"✅ Table {table} - OK")
            else:
                print(f"❌ Table {table} - MANQUANTE")
                return False
        
        # Test de l'utilisateur admin par défaut
        user = db.verify_user("admin", "admin123")
        if user:
            print("✅ Utilisateur admin par défaut - OK")
        else:
            print("❌ Utilisateur admin par défaut - ERREUR")
            return False
        
        # Nettoyer le fichier de test
        import os
        if os.path.exists("test_logitrak.db"):
            os.remove("test_logitrak.db")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de base de données: {e}")
        return False

def test_gui_basic():
    """Teste la création basique des composants GUI"""
    print("\n🖼️ Test des composants GUI...")
    
    try:
        import tkinter as tk
        
        # Test de création d'une fenêtre simple
        root = tk.Tk()
        root.withdraw()  # Cacher la fenêtre
        
        # Test des widgets de base
        frame = tk.Frame(root)
        label = tk.Label(frame, text="Test")
        entry = tk.Entry(frame)
        button = tk.Button(frame, text="Test")
        
        print("✅ Widgets Tkinter de base - OK")
        
        # Test ttk
        from tkinter import ttk
        combo = ttk.Combobox(frame)
        tree = ttk.Treeview(frame)
        
        print("✅ Widgets TTK - OK")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Erreur GUI: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 LOGITRAK - Tests de configuration\n")
    
    all_tests_passed = True
    
    # Test des imports
    if not test_imports():
        all_tests_passed = False
    
    # Test de la base de données
    if not test_database():
        all_tests_passed = False
    
    # Test des composants GUI
    if not test_gui_basic():
        all_tests_passed = False
    
    print("\n" + "="*50)
    if all_tests_passed:
        print("🎉 Tous les tests sont passés avec succès !")
        print("✅ L'application LOGITRAK est prête à être utilisée.")
        print("\nPour lancer l'application :")
        print("   python main.py")
        print("\nCompte par défaut :")
        print("   Utilisateur: admin")
        print("   Mot de passe: admin123")
    else:
        print("❌ Certains tests ont échoué.")
        print("🔧 Veuillez corriger les erreurs avant de lancer l'application.")
    
    print("="*50)

if __name__ == "__main__":
    main()
