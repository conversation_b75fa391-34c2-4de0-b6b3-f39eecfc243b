#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration de LOGITRAK
Paramètres personnalisables de l'application
"""

# Configuration de la base de données
DATABASE_CONFIG = {
    'filename': 'logitrak.db',
    'backup_enabled': True,
    'backup_interval_days': 7
}

# Configuration de l'interface
UI_CONFIG = {
    'theme': 'default',  # default, dark, light
    'window_size': '1200x700',
    'font_family': 'Arial',
    'font_size': 10,
    'colors': {
        'primary': '#3498db',
        'success': '#27ae60',
        'warning': '#f39c12',
        'danger': '#e74c3c',
        'info': '#2c3e50',
        'light': '#ecf0f1',
        'dark': '#34495e'
    }
}

# Configuration des stocks
STOCK_CONFIG = {
    'low_stock_threshold': 5,      # Seuil d'alerte stock faible
    'critical_stock_threshold': 0,  # Seuil d'alerte rupture
    'default_unit': 'pièce',       # Unité par défaut
    'allow_negative_stock': True,   # Autoriser stock négatif
    'auto_reorder_enabled': False   # Commande automatique (futur)
}

# Configuration des rapports
REPORTS_CONFIG = {
    'default_period': 'monthly',    # monthly, yearly, custom
    'chart_style': 'line',          # line, bar, pie
    'export_format': 'pdf',         # pdf, excel, csv
    'include_charts': True,
    'max_entries_display': 100
}

# Configuration de sécurité
SECURITY_CONFIG = {
    'password_min_length': 6,
    'session_timeout_minutes': 60,
    'max_login_attempts': 3,
    'require_password_change': False,
    'password_complexity': False    # Exiger majuscules, chiffres, etc.
}

# Configuration des notifications
NOTIFICATION_CONFIG = {
    'show_welcome_message': True,
    'show_stock_alerts': True,
    'show_success_messages': True,
    'auto_refresh_interval': 30     # secondes
}

# Configuration des données par défaut
DEFAULT_DATA = {
    'categories': [
        'Informatique',
        'Bureautique', 
        'Mobilier',
        'Communication',
        'Maintenance',
        'Consommables'
    ],
    'units': [
        'pièce',
        'lot',
        'boîte',
        'ramette',
        'carton',
        'kg',
        'litre',
        'mètre'
    ],
    'sources': [
        'Fournisseur A',
        'Fournisseur B',
        'Achat direct',
        'Transfert',
        'Retour client',
        'Inventaire'
    ],
    'destinations': [
        'Service IT',
        'Comptabilité',
        'Direction',
        'Accueil',
        'Salle de réunion',
        'Maintenance',
        'Employé'
    ]
}

# Configuration de l'application
APP_CONFIG = {
    'name': 'LOGITRAK',
    'version': '1.0.0',
    'description': 'Système de Gestion de Stock',
    'author': 'Équipe LOGITRAK',
    'debug_mode': False,
    'auto_backup': True,
    'check_updates': False
}

# Fonctions utilitaires pour accéder à la configuration
def get_config(section, key=None, default=None):
    """Récupère une valeur de configuration"""
    configs = {
        'database': DATABASE_CONFIG,
        'ui': UI_CONFIG,
        'stock': STOCK_CONFIG,
        'reports': REPORTS_CONFIG,
        'security': SECURITY_CONFIG,
        'notification': NOTIFICATION_CONFIG,
        'default_data': DEFAULT_DATA,
        'app': APP_CONFIG
    }
    
    if section not in configs:
        return default
    
    if key is None:
        return configs[section]
    
    return configs[section].get(key, default)

def update_config(section, key, value):
    """Met à jour une valeur de configuration"""
    configs = {
        'database': DATABASE_CONFIG,
        'ui': UI_CONFIG,
        'stock': STOCK_CONFIG,
        'reports': REPORTS_CONFIG,
        'security': SECURITY_CONFIG,
        'notification': NOTIFICATION_CONFIG,
        'default_data': DEFAULT_DATA,
        'app': APP_CONFIG
    }
    
    if section in configs and key in configs[section]:
        configs[section][key] = value
        return True
    return False

# Validation de la configuration
def validate_config():
    """Valide la configuration actuelle"""
    errors = []
    
    # Vérifier les seuils de stock
    if STOCK_CONFIG['low_stock_threshold'] < 0:
        errors.append("Le seuil de stock faible doit être positif")
    
    # Vérifier la taille de la fenêtre
    try:
        width, height = UI_CONFIG['window_size'].split('x')
        if int(width) < 800 or int(height) < 600:
            errors.append("La taille de fenêtre doit être au moins 800x600")
    except:
        errors.append("Format de taille de fenêtre invalide (utilisez WIDTHxHEIGHT)")
    
    # Vérifier la longueur minimale du mot de passe
    if SECURITY_CONFIG['password_min_length'] < 4:
        errors.append("La longueur minimale du mot de passe doit être au moins 4")
    
    return errors

# Configuration par défaut pour les nouveaux utilisateurs
def get_default_user_preferences():
    """Retourne les préférences par défaut pour un nouvel utilisateur"""
    return {
        'theme': UI_CONFIG['theme'],
        'notifications_enabled': True,
        'auto_refresh': True,
        'show_tooltips': True,
        'compact_view': False
    }

# Sauvegarde et chargement de la configuration
def save_config_to_file(filename='logitrak_config.json'):
    """Sauvegarde la configuration dans un fichier JSON"""
    import json
    
    config_data = {
        'database': DATABASE_CONFIG,
        'ui': UI_CONFIG,
        'stock': STOCK_CONFIG,
        'reports': REPORTS_CONFIG,
        'security': SECURITY_CONFIG,
        'notification': NOTIFICATION_CONFIG,
        'default_data': DEFAULT_DATA,
        'app': APP_CONFIG
    }
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de la configuration: {e}")
        return False

def load_config_from_file(filename='logitrak_config.json'):
    """Charge la configuration depuis un fichier JSON"""
    import json
    import os
    
    if not os.path.exists(filename):
        return False
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # Mettre à jour les configurations globales
        global DATABASE_CONFIG, UI_CONFIG, STOCK_CONFIG, REPORTS_CONFIG
        global SECURITY_CONFIG, NOTIFICATION_CONFIG, DEFAULT_DATA, APP_CONFIG
        
        DATABASE_CONFIG.update(config_data.get('database', {}))
        UI_CONFIG.update(config_data.get('ui', {}))
        STOCK_CONFIG.update(config_data.get('stock', {}))
        REPORTS_CONFIG.update(config_data.get('reports', {}))
        SECURITY_CONFIG.update(config_data.get('security', {}))
        NOTIFICATION_CONFIG.update(config_data.get('notification', {}))
        DEFAULT_DATA.update(config_data.get('default_data', {}))
        APP_CONFIG.update(config_data.get('app', {}))
        
        return True
    except Exception as e:
        print(f"Erreur lors du chargement de la configuration: {e}")
        return False

# Initialisation
if __name__ == "__main__":
    # Test de la configuration
    errors = validate_config()
    if errors:
        print("❌ Erreurs de configuration détectées:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ Configuration valide")
    
    # Exemple d'utilisation
    print(f"\nApplication: {get_config('app', 'name')} v{get_config('app', 'version')}")
    print(f"Seuil stock faible: {get_config('stock', 'low_stock_threshold')}")
    print(f"Couleur principale: {get_config('ui', 'colors')['primary']}")
