#!/usr/bin/env python3
"""
Script pour ajouter des données de démonstration à LOGITRAK
"""

from database import Database
from datetime import datetime, timedelta
import random

def add_demo_products(db):
    """Ajoute des produits de démonstration"""
    print("📦 Ajout des produits de démonstration...")
    
    products = [
        ("ORD001", "Ordinateur portable Dell", "Informatique", "pièce"),
        ("SOU002", "Souris optique", "Informatique", "pièce"),
        ("CLV003", "Clavier AZERTY", "Informatique", "pièce"),
        ("ECR004", "Écran 24 pouces", "Informatique", "pièce"),
        ("CAB005", "Câble HDMI", "Informatique", "pièce"),
        ("PAP006", "Papier A4", "Bureautique", "ramette"),
        ("STY007", "<PERSON>ylo bleu", "Bureautique", "pièce"),
        ("CAH008", "<PERSON><PERSON>er 200 pages", "Bureautique", "pièce"),
        ("AGR009", "Agrafeuse", "Bureautique", "pièce"),
        ("TRO010", "Trombones", "Bureautique", "boîte"),
        ("TAB011", "Table de bureau", "Mobilier", "pièce"),
        ("CHA012", "Chaise de bureau", "Mobilier", "pièce"),
        ("ARM013", "Armoire métallique", "Mobilier", "pièce"),
        ("LAM014", "Lampe de bureau", "Mobilier", "pièce"),
        ("TEL015", "Téléphone fixe", "Communication", "pièce"),
    ]
    
    for code, nom, categorie, unite in products:
        product_id = db.add_product(code, nom, categorie, unite)
        if product_id:
            print(f"✅ Produit ajouté: {code} - {nom}")
        else:
            print(f"⚠️ Produit déjà existant: {code}")

def add_demo_entries(db):
    """Ajoute des entrées de démonstration"""
    print("\n➕ Ajout des entrées de démonstration...")
    
    # Récupérer tous les produits
    products = db.get_products()
    
    # Générer des entrées sur les 30 derniers jours
    for i in range(50):  # 50 entrées aléatoires
        product = random.choice(products)
        product_id = product[0]
        
        # Date aléatoire dans les 30 derniers jours
        days_ago = random.randint(1, 30)
        date_entree = (datetime.now() - timedelta(days=days_ago)).strftime("%Y-%m-%d")
        
        # Quantité aléatoire
        quantite = random.randint(1, 20)
        
        # Sources possibles
        sources = ["Fournisseur A", "Fournisseur B", "Achat direct", "Transfert", "Retour client"]
        source = random.choice(sources)
        
        # Notes possibles
        notes = ["Livraison normale", "Commande urgente", "Réapprovisionnement", "Stock de sécurité", ""]
        note = random.choice(notes)
        
        # Utilisateur (admin par défaut, ID = 1)
        user_id = 1
        
        try:
            db.add_entry(product_id, date_entree, quantite, source, note, user_id)
            print(f"✅ Entrée ajoutée: {product[2]} - {quantite} {product[4]}")
        except Exception as e:
            print(f"❌ Erreur lors de l'ajout d'entrée: {e}")

def add_demo_exits(db):
    """Ajoute des sorties de démonstration"""
    print("\n➖ Ajout des sorties de démonstration...")
    
    # Récupérer tous les produits
    products = db.get_products()
    
    # Générer des sorties sur les 30 derniers jours
    for i in range(30):  # 30 sorties aléatoires
        product = random.choice(products)
        product_id = product[0]
        
        # Vérifier le stock actuel
        current_stock = product[5]  # stock_actuel
        if current_stock <= 0:
            continue  # Passer si pas de stock
        
        # Date aléatoire dans les 30 derniers jours
        days_ago = random.randint(1, 30)
        date_sortie = (datetime.now() - timedelta(days=days_ago)).strftime("%Y-%m-%d")
        
        # Quantité aléatoire (max 50% du stock actuel)
        max_quantity = max(1, int(current_stock * 0.5))
        quantite = random.randint(1, max_quantity)
        
        # Destinations possibles
        destinations = ["Service IT", "Comptabilité", "Direction", "Accueil", "Salle de réunion", "Employé"]
        destination = random.choice(destinations)
        
        # Motifs possibles
        motifs = ["Attribution", "Remplacement", "Maintenance", "Prêt temporaire", "Installation"]
        motif = random.choice(motifs)
        
        # Utilisateur (admin par défaut, ID = 1)
        user_id = 1
        
        try:
            db.add_exit(product_id, date_sortie, quantite, destination, motif, user_id)
            print(f"✅ Sortie ajoutée: {product[2]} - {quantite} {product[4]}")
        except Exception as e:
            print(f"❌ Erreur lors de l'ajout de sortie: {e}")

def create_demo_users(db):
    """Crée des utilisateurs de démonstration"""
    print("\n👤 Création des utilisateurs de démonstration...")
    
    users = [
        ("user1", "password123", "user"),
        ("gestionnaire", "gestion123", "user"),
        ("superviseur", "super123", "admin"),
    ]
    
    for username, password, role in users:
        if db.add_user(username, password, role):
            print(f"✅ Utilisateur créé: {username} ({role})")
        else:
            print(f"⚠️ Utilisateur déjà existant: {username}")

def main():
    """Fonction principale"""
    print("🎭 LOGITRAK - Génération de données de démonstration\n")
    
    try:
        # Initialiser la base de données
        db = Database()
        
        # Ajouter les données de démonstration
        add_demo_products(db)
        add_demo_entries(db)
        add_demo_exits(db)
        create_demo_users(db)
        
        print("\n" + "="*60)
        print("🎉 Données de démonstration ajoutées avec succès !")
        print("\n📊 Résumé :")
        
        # Afficher un résumé
        products = db.get_products()
        print(f"   • {len(products)} produits")
        
        entries = db.get_recent_entries(1000)  # Toutes les entrées
        print(f"   • {len(entries)} entrées")
        
        exits = db.get_recent_exits(1000)  # Toutes les sorties
        print(f"   • {len(exits)} sorties")
        
        print("\n🔐 Comptes utilisateur disponibles :")
        print("   • admin / admin123 (Administrateur)")
        print("   • user1 / password123 (Utilisateur)")
        print("   • gestionnaire / gestion123 (Utilisateur)")
        print("   • superviseur / super123 (Administrateur)")
        
        print("\n🚀 Vous pouvez maintenant lancer l'application :")
        print("   python main.py")
        print("="*60)
        
    except Exception as e:
        print(f"❌ Erreur lors de la génération des données: {e}")

if __name__ == "__main__":
    main()
