import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class ExitManager:
    def __init__(self, parent, database, auth_manager):
        self.parent = parent
        self.database = database
        self.auth_manager = auth_manager
        
    def show(self):
        """Affiche l'interface de gestion des sorties"""
        # Titre
        title_label = tk.Label(
            self.parent,
            text="➖ Enregistrement des Sorties",
            font=('Arial', 16, 'bold'),
            bg='white'
        )
        title_label.pack(pady=20)
        
        # Frame principal
        main_frame = tk.Frame(self.parent, bg='white')
        main_frame.pack(fill='both', expand=True, padx=50, pady=20)
        
        # Formulaire de sortie
        self.create_exit_form(main_frame)
    
    def create_exit_form(self, parent):
        """Crée le formulaire de sortie"""
        # Frame pour le formulaire
        form_frame = tk.LabelFrame(parent, text="Nouvelle sortie", font=('Arial', 12, 'bold'), bg='white')
        form_frame.pack(fill='x', pady=20)
        
        # Grid configuration
        form_frame.grid_columnconfigure(1, weight=1)
        
        # Sélection du produit
        tk.Label(form_frame, text="Produit:", bg='white').grid(row=0, column=0, sticky='w', padx=10, pady=10)
        
        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(form_frame, textvariable=self.product_var, state='readonly', width=40)
        self.product_combo.grid(row=0, column=1, sticky='ew', padx=10, pady=10)
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_selected)
        
        # Charger les produits
        self.load_products()
        
        # Affichage du stock actuel
        self.stock_label = tk.Label(form_frame, text="Stock actuel: --", bg='white', fg='#e74c3c', font=('Arial', 10, 'bold'))
        self.stock_label.grid(row=0, column=2, sticky='w', padx=10, pady=10)
        
        # Date de sortie
        tk.Label(form_frame, text="Date de sortie:", bg='white').grid(row=1, column=0, sticky='w', padx=10, pady=10)
        
        self.date_var = tk.StringVar(value=datetime.now().strftime("%d/%m/%Y"))
        date_entry = tk.Entry(form_frame, textvariable=self.date_var, width=20)
        date_entry.grid(row=1, column=1, sticky='w', padx=10, pady=10)
        
        # Quantité
        tk.Label(form_frame, text="Quantité:", bg='white').grid(row=2, column=0, sticky='w', padx=10, pady=10)
        
        self.quantity_var = tk.StringVar()
        quantity_entry = tk.Entry(form_frame, textvariable=self.quantity_var, width=20)
        quantity_entry.grid(row=2, column=1, sticky='w', padx=10, pady=10)
        
        # Destination
        tk.Label(form_frame, text="Destination:", bg='white').grid(row=3, column=0, sticky='w', padx=10, pady=10)
        
        self.destination_var = tk.StringVar()
        destination_entry = tk.Entry(form_frame, textvariable=self.destination_var, width=40)
        destination_entry.grid(row=3, column=1, sticky='ew', padx=10, pady=10)
        
        # Motif
        tk.Label(form_frame, text="Motif:", bg='white').grid(row=4, column=0, sticky='nw', padx=10, pady=10)
        
        self.motif_text = tk.Text(form_frame, height=3, width=40)
        self.motif_text.grid(row=4, column=1, sticky='ew', padx=10, pady=10)
        
        # Boutons
        buttons_frame = tk.Frame(form_frame, bg='white')
        buttons_frame.grid(row=5, column=0, columnspan=3, pady=20)
        
        save_btn = tk.Button(
            buttons_frame,
            text="📤 Enregistrer la sortie",
            font=('Arial', 10, 'bold'),
            bg='#e74c3c',
            fg='white',
            command=self.save_exit,
            cursor='hand2',
            width=20
        )
        save_btn.pack(side='left', padx=10)
        
        clear_btn = tk.Button(
            buttons_frame,
            text="🗑️ Effacer",
            font=('Arial', 10),
            bg='#95a5a6',
            fg='white',
            command=self.clear_form,
            cursor='hand2',
            width=15
        )
        clear_btn.pack(side='left', padx=10)
        
        # Historique des sorties récentes
        self.create_recent_exits(parent)
    
    def load_products(self):
        """Charge la liste des produits dans le combobox"""
        products = self.database.get_products()
        product_list = [f"{product[1]} - {product[2]}" for product in products]  # code - nom
        self.product_combo['values'] = product_list
        
        # Stocker la correspondance code -> (id, stock)
        self.product_map = {f"{product[1]} - {product[2]}": (product[0], product[5]) for product in products}
    
    def on_product_selected(self, event):
        """Met à jour l'affichage du stock quand un produit est sélectionné"""
        if self.product_var.get() in self.product_map:
            product_id, stock = self.product_map[self.product_var.get()]
            self.stock_label.config(text=f"Stock actuel: {stock}")
        else:
            self.stock_label.config(text="Stock actuel: --")
    
    def save_exit(self):
        """Enregistre une nouvelle sortie"""
        # Validation
        if not self.product_var.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un produit")
            return
        
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Erreur", "Veuillez saisir une quantité valide (nombre positif)")
            return
        
        # Vérifier le stock disponible
        if self.product_var.get() in self.product_map:
            product_id, current_stock = self.product_map[self.product_var.get()]
            if quantity > current_stock:
                if not messagebox.askyesno(
                    "Stock insuffisant", 
                    f"La quantité demandée ({quantity}) dépasse le stock disponible ({current_stock}).\n"
                    "Voulez-vous continuer quand même ?"
                ):
                    return
        
        # Validation de la date
        try:
            date_obj = datetime.strptime(self.date_var.get(), "%d/%m/%Y")
            date_str = date_obj.strftime("%Y-%m-%d")
        except ValueError:
            messagebox.showerror("Erreur", "Format de date invalide (JJ/MM/AAAA)")
            return
        
        # Récupérer les données
        product_id = self.product_map[self.product_var.get()][0]
        destination = self.destination_var.get().strip()
        motif = self.motif_text.get("1.0", tk.END).strip()
        user_id = self.auth_manager.get_user_id()
        
        # Enregistrer dans la base de données
        try:
            self.database.add_exit(product_id, date_str, quantity, destination, motif, user_id)
            messagebox.showinfo("Succès", f"Sortie enregistrée avec succès\nQuantité: {quantity}")
            self.clear_form()
            self.load_recent_exits()  # Rafraîchir l'historique
            self.load_products()  # Recharger pour mettre à jour les stocks
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
    
    def clear_form(self):
        """Efface le formulaire"""
        self.product_var.set("")
        self.date_var.set(datetime.now().strftime("%d/%m/%Y"))
        self.quantity_var.set("")
        self.destination_var.set("")
        self.motif_text.delete("1.0", tk.END)
        self.stock_label.config(text="Stock actuel: --")
    
    def create_recent_exits(self, parent):
        """Crée la section des sorties récentes"""
        # Frame pour l'historique
        history_frame = tk.LabelFrame(parent, text="Sorties récentes", font=('Arial', 12, 'bold'), bg='white')
        history_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # Tableau des sorties récentes
        columns = ('Date', 'Produit', 'Quantité', 'Destination', 'Utilisateur')
        self.exits_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=8)
        
        # Configuration des colonnes
        self.exits_tree.heading('Date', text='Date')
        self.exits_tree.heading('Produit', text='Produit')
        self.exits_tree.heading('Quantité', text='Quantité')
        self.exits_tree.heading('Destination', text='Destination')
        self.exits_tree.heading('Utilisateur', text='Utilisateur')
        
        self.exits_tree.column('Date', width=100)
        self.exits_tree.column('Produit', width=200)
        self.exits_tree.column('Quantité', width=100)
        self.exits_tree.column('Destination', width=150)
        self.exits_tree.column('Utilisateur', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(history_frame, orient='vertical', command=self.exits_tree.yview)
        self.exits_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.exits_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Charger les sorties récentes
        self.load_recent_exits()
    
    def load_recent_exits(self):
        """Charge les sorties récentes"""
        # Vider le tableau
        for item in self.exits_tree.get_children():
            self.exits_tree.delete(item)

        # Récupérer les sorties récentes
        try:
            exits = self.database.get_recent_exits(20)
            for exit in exits:
                date_str = exit[0]
                # Convertir la date au format français
                try:
                    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                    date_formatted = date_obj.strftime("%d/%m/%Y")
                except:
                    date_formatted = date_str

                self.exits_tree.insert('', 'end', values=(
                    date_formatted,
                    exit[1],  # nom produit
                    exit[2],  # quantité
                    exit[3] or '',  # destination
                    exit[4]   # utilisateur
                ))
        except Exception as e:
            self.exits_tree.insert('', 'end', values=('--', f'Erreur: {str(e)}', '--', '--', '--'))
