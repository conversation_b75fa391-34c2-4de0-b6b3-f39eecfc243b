# 🚀 Guide d'Installation et de Lancement - LOGITRAK

## 📋 Prérequis

- **Python 3.7+** installé sur votre système
- **Tkinter** (généralement inclus avec Python)
- Accès à Internet pour installer les dépendances (optionnel)

## 🔧 Installation

### Étape 1: Vérification de Python
```bash
python --version
```
Vous devriez voir quelque chose comme `Python 3.x.x`

### Étape 2: Installation des dépendances (optionnel)
Pour l'interface complète avec graphiques :
```bash
pip install -r requirements.txt
```

**Note :** L'application fonctionne sans ces dépendances en mode simplifié.

## 🚀 Lancement de l'Application

### Méthode 1: Script de lancement automatique (Recommandé)
```bash
python run_logitrak.py
```

Ce script :
- ✅ Vérifie automatiquement les dépendances
- 🔧 Corrige les problèmes de base de données
- 📊 Crée des données de test
- 🚀 Lance l'application appropriée

### Méthode 2: Lancement direct
```bash
python main.py
```

### Méthode 3: Version simplifiée
```bash
python launch_simple.py
```

## 🔐 Première Connexion

### Compte Administrateur par défaut
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`

⚠️ **Important :** Changez ce mot de passe après la première connexion !

## 📊 Données de Démonstration

Pour ajouter des données de test :
```bash
python demo_data.py
```

Cela créera :
- 15 produits d'exemple
- Entrées et sorties aléatoires
- Utilisateurs supplémentaires

## 🗂️ Structure des Fichiers

```
LOGITRAK/
├── 🚀 run_logitrak.py          # Script de lancement principal (RECOMMANDÉ)
├── 📱 main.py                  # Application complète
├── 🔧 launch_simple.py         # Version simplifiée
├── 🗃️ database.py              # Gestion base de données
├── 🔐 auth.py                  # Authentification
├── 📋 requirements.txt         # Dépendances
├── 🎭 demo_data.py             # Données de démonstration
├── 🧪 test_imports.py          # Tests de configuration
├── 📖 README.md               # Documentation complète
├── 📖 GUIDE_INSTALLATION.md   # Ce guide
├── gui/                       # Interface graphique
│   ├── login_window.py        # Fenêtre de connexion
│   ├── main_window.py         # Fenêtre principale
│   ├── product_manager.py     # Gestion produits
│   ├── entry_manager.py       # Gestion entrées
│   ├── exit_manager.py        # Gestion sorties
│   └── reports.py             # Rapports
└── 🗃️ logitrak.db             # Base de données (créée automatiquement)
```

## 🎯 Utilisation Rapide

### 1. Premier lancement
```bash
python run_logitrak.py
```

### 2. Connexion
- Utilisateur: `admin`
- Mot de passe: `admin123`

### 3. Navigation
- **🗂️ Produits** : Gérer le catalogue (admin uniquement)
- **➕ Entrées** : Enregistrer les arrivées de stock
- **➖ Sorties** : Enregistrer les sorties de stock
- **📊 Rapports** : Consulter les statistiques (admin uniquement)

## 🔧 Résolution de Problèmes

### Problème : "Module not found"
**Solution :**
```bash
pip install -r requirements.txt
```

### Problème : "Database is locked"
**Solution :**
```bash
python run_logitrak.py
```
Le script corrige automatiquement ce problème.

### Problème : Interface ne s'affiche pas
**Solutions :**
1. Vérifiez que Tkinter est installé :
   ```bash
   python -c "import tkinter; print('Tkinter OK')"
   ```
2. Utilisez la version simplifiée :
   ```bash
   python launch_simple.py
   ```

### Problème : Erreurs de graphiques
**Solution :** Utilisez la version sans matplotlib :
```bash
python launch_simple.py
```

## 🧪 Tests et Vérification

### Test complet de l'installation
```bash
python test_imports.py
```

### Test de la base de données
```bash
python test_db.py
```

## 📱 Fonctionnalités Disponibles

### ✅ Version Complète (avec dépendances)
- Interface graphique complète
- Graphiques et statistiques
- Rapports détaillés
- Export PDF/Excel (à venir)

### ✅ Version Simplifiée (sans dépendances)
- Interface de base
- Gestion des produits
- Entrées/sorties
- Statistiques simples

## 🔒 Sécurité

- Mots de passe hashés (SHA-256)
- Gestion des rôles (admin/user)
- Base de données SQLite locale
- Pas de connexion réseau requise

## 📞 Support

### En cas de problème :

1. **Vérifiez les prérequis** (Python 3.7+)
2. **Relancez avec le script automatique** : `python run_logitrak.py`
3. **Consultez les messages d'erreur** dans le terminal
4. **Utilisez la version simplifiée** si nécessaire

### Commandes utiles :

```bash
# Vérification complète
python test_imports.py

# Lancement automatique (recommandé)
python run_logitrak.py

# Version de secours
python launch_simple.py

# Ajout de données de test
python demo_data.py
```

## 🎉 Félicitations !

Votre système LOGITRAK est maintenant prêt à l'emploi !

**Prochaines étapes :**
1. 🔐 Connectez-vous avec admin/admin123
2. 🗂️ Ajoutez vos premiers produits
3. ➕ Enregistrez vos premières entrées
4. 📊 Consultez vos rapports

---

**Version :** 1.0.0  
**Dernière mise à jour :** Décembre 2024  
**Support :** Documentation complète dans README.md
