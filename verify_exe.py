#!/usr/bin/env python3
"""
Vérification de l'exécutable LOGITRAK créé
"""

import os
import sys
from pathlib import Path

def check_exe_files():
    """Vérifie la présence des fichiers de l'exécutable"""
    print("📁 Vérification des fichiers de l'exécutable...")
    
    dist_path = Path("dist")
    if not dist_path.exists():
        print("❌ Dossier 'dist' non trouvé")
        return False
    
    required_files = [
        "LOGITRAK.exe",
        "installer.bat", 
        "README_EXE.txt"
    ]
    
    all_present = True
    for file in required_files:
        file_path = dist_path / file
        if file_path.exists():
            size = file_path.stat().st_size
            if file == "LOGITRAK.exe":
                size_mb = size / (1024 * 1024)
                print(f"✅ {file} ({size_mb:.1f} MB)")
            else:
                print(f"✅ {file}")
        else:
            print(f"❌ {file} - MANQUANT")
            all_present = False
    
    return all_present

def check_exe_properties():
    """Vérifie les propriétés de l'exécutable"""
    exe_path = Path("dist/LOGITRAK.exe")
    
    if not exe_path.exists():
        print("❌ LOGITRAK.exe non trouvé")
        return False
    
    print("\n📊 Propriétés de l'exécutable:")
    
    # Taille
    size = exe_path.stat().st_size
    size_mb = size / (1024 * 1024)
    print(f"   📏 Taille: {size_mb:.1f} MB ({size:,} octets)")
    
    # Évaluation de la taille
    if size_mb < 30:
        print("   ⚠️ Taille petite - vérifiez que tous les modules sont inclus")
    elif size_mb < 100:
        print("   ✅ Taille normale pour PyInstaller")
    else:
        print("   ⚠️ Taille importante - considérez l'optimisation")
    
    return True

def check_support_files():
    """Vérifie les fichiers de support"""
    print("\n📋 Vérification des fichiers de support:")
    
    support_files = {
        "installer.bat": "Script d'installation automatique",
        "README_EXE.txt": "Guide utilisateur pour l'exécutable"
    }
    
    for file, description in support_files.items():
        file_path = Path("dist") / file
        if file_path.exists():
            print(f"✅ {file} - {description}")
        else:
            print(f"❌ {file} - MANQUANT")

def create_distribution_package():
    """Crée un package de distribution"""
    print("\n📦 Création du package de distribution...")
    
    from datetime import datetime
    date_str = datetime.now().strftime("%Y-%m-%d")
    package_name = f"LOGITRAK_v1.0_{date_str}"
    
    package_path = Path(package_name)
    
    # Créer le dossier s'il n'existe pas
    if not package_path.exists():
        package_path.mkdir()
        print(f"✅ Dossier créé: {package_name}")
    
    # Copier les fichiers essentiels
    import shutil
    
    files_to_copy = [
        ("dist/LOGITRAK.exe", "LOGITRAK.exe"),
        ("dist/installer.bat", "installer.bat"),
        ("dist/README_EXE.txt", "README_EXE.txt"),
        ("README.md", "README_COMPLET.md"),
        ("GUIDE_INSTALLATION.md", "GUIDE_INSTALLATION.md"),
        ("GUIDE_EXECUTABLE.md", "GUIDE_EXECUTABLE.md")
    ]
    
    for src, dst in files_to_copy:
        src_path = Path(src)
        dst_path = package_path / dst
        
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"✅ Copié: {dst}")
        else:
            print(f"⚠️ Source manquante: {src}")
    
    # Créer un fichier de version
    version_content = f"""LOGITRAK - Système de Gestion de Stock
Version: 1.0.0
Date de compilation: {date_str}
Plateforme: Windows

Contenu du package:
- LOGITRAK.exe : Application principale
- installer.bat : Script d'installation automatique  
- README_EXE.txt : Guide d'utilisation rapide
- README_COMPLET.md : Documentation complète
- GUIDE_INSTALLATION.md : Guide d'installation détaillé
- GUIDE_EXECUTABLE.md : Guide de création d'exécutable

Instructions d'installation:
1. Méthode automatique: Double-cliquez sur installer.bat
2. Méthode manuelle: Double-cliquez sur LOGITRAK.exe

Connexion par défaut:
- Utilisateur: admin
- Mot de passe: admin123

Support: Consultez les fichiers README inclus
"""
    
    version_path = package_path / "VERSION.txt"
    with open(version_path, 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print(f"✅ Fichier VERSION.txt créé")
    
    return package_name

def generate_summary():
    """Génère un résumé final"""
    print("\n" + "="*60)
    print("🎉 LOGITRAK - EXÉCUTABLE CRÉÉ AVEC SUCCÈS !")
    print("="*60)
    
    exe_path = Path("dist/LOGITRAK.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"📊 Taille de l'exécutable: {size_mb:.1f} MB")
    
    print("\n📁 Fichiers créés:")
    print("   • dist/LOGITRAK.exe - Application principale")
    print("   • dist/installer.bat - Script d'installation")
    print("   • dist/README_EXE.txt - Guide utilisateur")
    
    print("\n🚀 Pour distribuer l'application:")
    print("   1. Partagez le dossier 'dist' complet")
    print("   2. Ou utilisez le package de distribution créé")
    print("   3. L'utilisateur final lance 'installer.bat'")
    
    print("\n🔐 Connexion par défaut:")
    print("   • Utilisateur: admin")
    print("   • Mot de passe: admin123")
    
    print("\n✅ L'application est prête pour la distribution !")
    print("="*60)

def main():
    """Fonction principale de vérification"""
    print("🔍 LOGITRAK - Vérification de l'Exécutable")
    print("="*50)
    
    # Vérifications
    if not check_exe_files():
        print("\n❌ Fichiers manquants - relancez la construction")
        return False
    
    if not check_exe_properties():
        print("\n❌ Problème avec l'exécutable")
        return False
    
    check_support_files()
    
    # Créer le package de distribution
    package_name = create_distribution_package()
    
    # Résumé final
    generate_summary()
    
    print(f"\n📦 Package de distribution: {package_name}/")
    
    return True

if __name__ == "__main__":
    main()
