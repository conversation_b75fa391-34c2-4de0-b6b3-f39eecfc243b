#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LOGITRAK - Système de Gestion de Stock
Application principale
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database
from auth import AuthManager
from gui.login_window import LoginWindow
from gui.main_window import MainWindow

class LogitrakApp:
    def __init__(self):
        self.database = None
        self.auth_manager = None
        self.login_window = None
        self.main_window = None
        
    def start(self):
        """Démarre l'application"""
        try:
            # Initialiser la base de données
            self.database = Database()
            
            # Initialiser le gestionnaire d'authentification
            self.auth_manager = AuthManager(self.database)
            
            # Afficher la fenêtre de connexion
            self.show_login()
            
        except Exception as e:
            messagebox.showerror("<PERSON><PERSON>ur", f"Erreur lors du démarrage de l'application:\n{str(e)}")
            sys.exit(1)
    
    def show_login(self):
        """Affiche la fenêtre de connexion"""
        self.login_window = LoginWindow(self.auth_manager, self.on_login_success)
        self.login_window.show()
    
    def on_login_success(self):
        """Appelé après une connexion réussie"""
        # Fermer la fenêtre de connexion si elle existe
        if self.login_window:
            self.login_window.close()
        
        # Afficher la fenêtre principale
        self.show_main_window()
    
    def show_main_window(self):
        """Affiche la fenêtre principale"""
        self.main_window = MainWindow(self.database, self.auth_manager, self.on_logout)
        self.main_window.show()
    
    def on_logout(self):
        """Appelé lors de la déconnexion"""
        # Fermer la fenêtre principale si elle existe
        if self.main_window:
            self.main_window.close()
        
        # Réafficher la fenêtre de connexion
        self.show_login()

def main():
    """Point d'entrée principal"""
    try:
        # Créer et démarrer l'application
        app = LogitrakApp()
        app.start()
        
    except KeyboardInterrupt:
        print("\nApplication interrompue par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"Erreur fatale: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
